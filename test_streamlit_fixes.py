#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试streamlit修复的脚本
"""

import pandas as pd
import numpy as np
import sys
import os
import tempfile
import time

# 添加当前目录到路径
sys.path.append('.')

import importlib.util
spec = importlib.util.spec_from_file_location("local_ai_fixed", "local_ai_fixedV0.8.py")
local_ai_fixed = importlib.util.module_from_spec(spec)
spec.loader.exec_module(local_ai_fixed)
ExcelSimpleApp = local_ai_fixed.ExcelSimpleApp

def test_streamlit_template_fix():
    """测试streamlit模板中的变量修复"""
    print("🧪 测试streamlit模板变量修复...")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '姓名': ['张三', '李四', '王五', '赵六'],
        '年龄': [25, 30, 35, 28],
        '城市': ['北京', '上海', '广州', '深圳'],
        '收入': [8000, 12000, 15000, 9500]
    })
    
    # 创建应用实例
    app = ExcelSimpleApp()
    app.current_df = test_data
    
    try:
        # 测试创建streamlit应用文件
        app_file, data_file = app._create_streamlit_app_file(test_data, "测试数据", "test")
        
        if app_file and data_file:
            print("✅ streamlit应用文件创建成功")
            
            # 读取生成的应用代码
            with open(app_file, 'r', encoding='utf-8') as f:
                app_code = f.read()
            
            # 检查是否包含未转义的{title}
            if '{title}' in app_code and '{{title}}' not in app_code:
                print("❌ 发现未转义的{title}变量")
                return False
            elif '{{title}}' in app_code:
                print("✅ title变量已正确转义")
            
            # 检查是否包含matplotlib导入
            if 'import matplotlib.pyplot as plt' in app_code:
                print("✅ matplotlib支持已添加")
            else:
                print("❌ 缺少matplotlib支持")
                return False
                
            # 检查是否包含plotly导入
            if 'import plotly.express as px' in app_code:
                print("✅ plotly支持已添加")
            else:
                print("❌ 缺少plotly支持")
                return False
                
            # 检查是否包含数据透视表功能
            if '数据透视表' in app_code:
                print("✅ 数据透视表功能已添加")
            else:
                print("❌ 缺少数据透视表功能")
                return False
            
            # 清理临时文件
            try:
                os.remove(app_file)
                os.remove(data_file)
            except:
                pass
                
            return True
        else:
            print("❌ streamlit应用文件创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_ai_dialog_preview_save():
    """测试AI对话中的保存预览功能"""
    print("\n🧪 测试AI对话保存预览功能...")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '姓名': ['张三', '李四', '王五'],
        '销量': [100, 150, 120]
    })
    
    # 创建应用实例
    app = ExcelSimpleApp()
    app.current_df = test_data
    
    try:
        # 模拟预览结果
        preview_result = pd.DataFrame({
            '姓名': ['张三', '李四', '王五'],
            '销量': [100, 150, 120],
            '销量分类': ['中等', '高', '中等']
        })
        
        # 设置预览结果
        app.preview_result_df = preview_result
        app.preview_code = "df['销量分类'] = df['销量'].apply(lambda x: '高' if x > 140 else '中等')"
        
        # 检查预览结果是否正确保存
        if app.preview_result_df is not None:
            print("✅ 预览结果已正确保存")
            
            # 检查预览结果的形状
            if app.preview_result_df.shape == (3, 3):
                print("✅ 预览结果数据形状正确")
                return True
            else:
                print(f"❌ 预览结果数据形状错误: {app.preview_result_df.shape}")
                return False
        else:
            print("❌ 预览结果未保存")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试streamlit相关修复...")
    
    test_results = []
    
    # 测试1: streamlit模板变量修复
    test_results.append(test_streamlit_template_fix())
    
    # 测试2: AI对话保存预览功能
    test_results.append(test_ai_dialog_preview_save())
    
    # 汇总结果
    print("\n📊 测试结果汇总:")
    print(f"✅ 通过: {sum(test_results)}")
    print(f"❌ 失败: {len(test_results) - sum(test_results)}")
    print(f"📈 成功率: {sum(test_results)/len(test_results)*100:.1f}%")
    
    if all(test_results):
        print("\n🎉 所有测试通过！streamlit相关修复成功")
        return True
    else:
        print("\n⚠️  部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
