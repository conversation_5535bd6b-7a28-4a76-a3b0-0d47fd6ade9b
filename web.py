import asyncio
import json
import uuid
import websockets

flow_id = "d5ac153e-7b00-49c5-80c7-2d5e8a311f25"
chat_id = str(uuid.uuid4()).replace('-', '')

# 从截图中提取的关键信息
# 注意：这个IP地址可能是局域网或特定网络地址，请确保您的电脑可以访问它。
WEBSOCKET_URL = f"ws://*********:3001/api/v2/chat/ws/{flow_id}?type=L1&&t=&chat_id={chat_id}"

# 从截图中构建的基础消息结构
# 我们将在每次发送时填充 "input" 字段
BASE_PAYLOAD = {
    "chatHistory": [],
    "flow_id": flow_id,
    "chat_id": chat_id, # 每次运行生成一个新的chat_id
    "name": "大模型对话-Qwen",
    "description": "大模型对话",
    "inputs": {
        "input": "", # 用户输入将填充这里
        "id": f"ConversationChain-A1J5d"
    }
}

async def chat_with_llm():
    """
    主函数，用于连接WebSocket并与大模型进行交互。
    """
    print("--- 开始与大模型聊天 ---")
    print(f"正在尝试连接到: {WEBSOCKET_URL}")
    
    try:
        # 使用 async with 语句来自动管理连接的建立和关闭
        async with websockets.connect(WEBSOCKET_URL) as websocket:
            print("--- 连接成功！可以开始聊天了 (输入 'exit' 或 'quit' 退出) ---")
            
            while True:
                # 1. 获取用户输入
                try:
                    user_message = input("You: ")
                except (KeyboardInterrupt, EOFError):
                    # 处理 Ctrl+C 或 Ctrl+D 的情况
                    print("\n--- 聊天结束 ---")
                    break

                if user_message.lower() in ["exit", "quit"]:
                    print("--- 聊天结束 ---")
                    break

                # 2. 构建要发送的JSON数据
                payload = BASE_PAYLOAD.copy()
                payload['inputs']['input'] = user_message
                
                # 将Python字典转换为JSON字符串
                message_to_send = json.dumps(payload)
                print(f"DEBUG: Sending -> {message_to_send}")

                # 3. 发送消息到服务器
                await websocket.send(message_to_send)
                
                # 4. 接收服务器的响应
                print("Bot: ", end="", flush=True)
                full_response = ""
                # LLM的回复可能是分多条消息（流式）返回的
                while True:
                    response_str = await websocket.recv()
                    # print(f"DEBUG: Received -> {response_str}") # 取消注释以查看原始回复
                    
                    try:
                        data = json.loads(response_str)
                        
                        #检查 type 和 category
                        if data.get("type") == "end" and data.get("category") == "answer":
                            bot_message = data.get("message", "未能解析回复。")
                            print(bot_message)
                            full_response = bot_message # 保存完整的回复
                            break # 找到最终答案，跳出内层循环，等待下一次用户输入
                        
                        # (可选) 如果有中间的流式消息需要显示，可以在这里添加逻辑
                        # 例如，如果流式消息有 'token' 字段:
                        # if data.get("type") == "stream" and "token" in data:
                        #     print(data["token"], end="", flush=True)

                    except json.JSONDecodeError:
                        print(f"无法解析的JSON响应: {response_str}")
                        break
                
                # (可选) 更新聊天历史，以便模型拥有上下文记忆
                # BASE_PAYLOAD['chatHistory'].append({"is_bot": False, "message": user_message})
                # BASE_PAYLOAD['chatHistory'].append({"is_bot": True, "message": full_response})


    except websockets.exceptions.ConnectionClosedError as e:
        print(f"\n--- 连接已关闭: {e} ---")
    except ConnectionRefusedError:
        print("\n--- 连接被拒绝 ---")
        print("请确认：")
        print("1. 服务器IP地址和端口号是否正确。")
        print("2. 您是否与服务器在同一个网络中。")
        print("3. 服务器程序正在运行。")
    except Exception as e:
        print(f"\n--- 发生未知错误: {e} ---")


if __name__ == "__main__":
    try:
        asyncio.run(chat_with_llm())
    except KeyboardInterrupt:
        print("\n--- 程序已中断 ---")