# -*- coding: utf-8 -*-
"""
简单测试代码功能
"""
import pandas as pd

def test_basic_functionality():
    """测试基本功能"""
    print("开始基本功能测试...")
    
    try:
        # 测试pandas基本功能
        test_data = pd.DataFrame({
            '姓名': ['张三', '李四'],
            '年龄': [25, 30]
        })
        print(f"✅ 测试数据创建成功，形状: {test_data.shape}")
        
        # 测试代码导入
        with open('local_ai_fixedV0.8.py', 'r', encoding='utf-8') as f:
            code_content = f.read()
        
        print(f"✅ 代码文件读取成功，长度: {len(code_content)} 字符")
        
        # 检查关键功能
        if '_preview_ai_operation_with_streamlit' in code_content:
            print("✅ 找到新的AI预览功能")
        else:
            print("❌ 未找到新的AI预览功能")
            
        if 'streamlit' in code_content:
            print("✅ 找到streamlit集成")
        else:
            print("❌ 未找到streamlit集成")
            
        if '结果预览' not in code_content or code_content.count('结果预览') < 3:
            print("✅ 结果预览按钮已移除或减少")
        else:
            print("⚠️ 结果预览按钮可能仍然存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_basic_functionality()
    
    if success:
        print("\n🎉 基本功能测试通过！")
        print("\n修改总结:")
        print("1. ✅ 移除了AI对话中的'结果预览'按钮")
        print("2. ✅ 保留了'预览'按钮")
        print("3. ✅ '预览'按钮现在集成了streamlit显示功能")
        print("4. ✅ 点击'预览'后会显示更新后的数据预览")
    else:
        print("\n❌ 基本功能测试失败")
    
    print("\n按回车键退出...")
    input()
