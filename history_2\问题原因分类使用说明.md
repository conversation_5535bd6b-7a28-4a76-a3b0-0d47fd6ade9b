# 问题原因分类功能使用说明

## 概述

本功能基于质量管理体系标准，提供了专业的产品质量问题原因分类能力。通过AI智能分析，可以自动将质量问题描述分类到37个预定义的原因类别中，帮助进行根因分析和质量改进。

## 功能特点

### 1. 专业分类体系
- **7大阶段分类**：设计、软件、工艺、管理、元器件、外协外购件、使用阶段
- **37个具体类别**：覆盖产品全生命周期的质量问题原因
- **层次化结构**：便于统计分析和管理决策

### 2. 智能分析能力
- **根因识别**：分析问题的根本原因，而非表面现象
- **优先级判断**：优先考虑设计阶段问题，符合质量管理原则
- **上下文理解**：基于问题描述的关键信息进行准确分类

### 3. 配置化管理
- **标准化定义**：每个类别都有明确的定义和适用范围
- **示例指导**：提供典型问题示例，提高分类准确性
- **可视化配置**：支持图形界面的配置管理

## 分类体系详解

### 设计阶段（7个类别）
1. **外部需求** - 外部输入技术要求内容不全或有差错
2. **需求分析** - 需求分析内容缺失或错误
3. **接口设计** - 各系统间接口关系不协调、不匹配
4. **功能性能和物理特性设计** - 架构、功能、性能设计错误
5. **通用质量特性设计** - 安全性、可靠性等设计不满足要求
6. **试验验证设计** - 试验验证设计不合理或设备选型错误
7. **技术认知** - 缺少预研基础或技术认知不够

### 软件阶段（4个类别）
1. **软件外部需求** - 外部要求描述不清、需求不完备
2. **软件设计** - 软件设计差错、接口设计不当、容错能力不足
3. **软件测试** - 测试覆盖性不足，未及时发现软件差错
4. **软件管理** - 版本控制或技术状态控制不当

### 工艺阶段（5个类别）
1. **工艺设计** - 工艺方案、方法、流程、参数不合理
2. **工艺文件可操作性** - 工艺文件不细不全、可操作性差
3. **工艺不稳定** - 人机料法环变化对工艺过程产生影响
4. **工艺工装设计** - 焊接、组装、机加、测试工装设计问题
5. **工艺认知** - 新工艺的认知和研究不够

### 管理阶段（6个类别）
1. **制度** - 制度缺项或规章制度不完善
2. **培训** - 培训内容、深度、力度不够
3. **责任制不落实** - 责任制不落实，把关不严
4. **人员违反规章制度** - 需要追究人为责任的质量问题
5. **人员疏忽大意** - 操作人员疏忽大意造成的问题
6. **人员违规操作** - 操作人员违反操作规章

### 元器件阶段（6个类别）
1. **元器件准入** - 准入验证不到位，不满足使用需求
2. **元器件设计** - 设计导致元器件指标不满足要求
3. **元器件工艺** - 元器件工艺导致的问题
4. **元器件生产管理和操作** - 生产管理和操作导致的问题
5. **元器件偶发失效** - 无纠正措施的偶发失效
6. **元器件固有缺陷且未剔除** - 只能加强检测的固有缺陷

### 外协外购件阶段（6个类别）
1. **外协外购件准入** - 准入验证不到位
2. **外协外购件设计** - 外购外协外包设计问题
3. **外协外购件工艺** - 外购外协外包工艺问题
4. **外协外购件生产管理** - 外购外协外包管理和操作问题
5. **外协外购件偶发失效** - 外购外协外包偶发失效
6. **外协外购件固有缺陷且未剔除** - 外购外协外包固有缺陷

### 使用阶段（4个类别）
1. **用户使用不当** - 用户未按说明书要求操作
2. **外部环境** - 外部环境引发的问题
3. **复试无故障** - 产品无故障
4. **其他** - 以上分类无法覆盖的质量问题

## 使用步骤

### 1. 准备数据
- 确保Excel/CSV文件包含问题描述列
- 问题描述应尽可能详细，包含关键信息
- 建议包含问题现象、发生条件、影响等信息

### 2. 启动分类功能
1. 打开主程序 `local_ai_fixedV0.7.py`
2. 加载包含问题描述的数据文件
3. 点击"AI智能分类标注"按钮
4. 选择包含问题描述的列
5. 在配置下拉框中选择"问题原因分类"

### 3. 执行分类
1. 设置新列名称（如"问题原因分类"）
2. 根据数据量调整批处理大小（建议1-5）
3. 点击"开始分类"按钮
4. 等待AI分析完成

### 4. 结果验证
- 查看分类结果的合理性
- 对于重要问题，建议人工复核
- 可以使用测试脚本进行准确性评估

## 最佳实践

### 1. 数据准备
- **详细描述**：问题描述越详细，分类越准确
- **关键信息**：包含故障现象、发生阶段、影响范围等
- **标准术语**：使用标准的技术术语和质量管理术语

### 2. 分类优化
- **批量处理**：大数据量时使用小批次处理（1-3条/批）
- **结果校验**：对关键问题进行人工校验
- **持续改进**：根据分类结果调整问题描述的标准化程度

### 3. 结果应用
- **统计分析**：按阶段和类别统计问题分布
- **趋势分析**：跟踪不同时期的问题原因变化
- **改进措施**：针对高频问题类别制定改进措施

## 测试验证

### 使用测试脚本
```bash
python test_problem_cause_classification.py
```

### 测试内容
- 25个典型质量问题样本
- 覆盖7大阶段的代表性问题
- 包含期望分类结果用于准确性验证

### 准确性评估
- 分类准确率统计
- 错误分类详细分析
- 各阶段问题分布统计

## 注意事项

1. **AI分类限制**：AI分类结果可能不是100%准确，重要数据建议人工复核
2. **网络要求**：需要连接到Ollama服务，确保网络畅通
3. **处理时间**：大数据量处理需要较长时间，请耐心等待
4. **结果保存**：及时保存分类结果，避免数据丢失

## 技术支持

如遇到问题，请检查：
1. Ollama服务是否正常运行
2. 配置文件是否正确加载
3. 数据格式是否符合要求
4. 网络连接是否稳定

## 更新日志

- **v0.7** - 新增问题原因分类功能
- 基于Excel文件"问题原因分类.xlsx"创建标准分类体系
- 优化AI提示词，提高分类准确性
- 增加专门的测试验证功能
