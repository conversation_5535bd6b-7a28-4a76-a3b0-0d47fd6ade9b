# -*- coding: utf-8 -*-
"""
演示新功能的脚本
"""
import pandas as pd
import sys
import os

def demo_new_features():
    """演示所有新增功能"""
    print("🎯 新功能演示")
    print("=" * 50)
    
    # 创建演示数据
    demo_data = pd.DataFrame({
        '产品名称': ['iPhone 15', 'Samsung S24', 'Xiaomi 14', 'OPPO Find X7', 'Vivo X100'],
        '价格': [7999, 6999, 3999, 4999, 4499],
        '销量': [1200, 800, 1500, 600, 900],
        '评分': [4.8, 4.6, 4.7, 4.5, 4.4],
        '上市时间': ['2023-09', '2024-01', '2024-02', '2024-01', '2023-12']
    })
    
    print("📊 演示数据:")
    print(demo_data.to_string(index=False))
    print()
    
    # 1. 演示markdown格式转换
    print("1️⃣ Markdown格式转换功能:")
    print("-" * 30)
    markdown_table = demo_data.to_markdown(index=False)
    print(markdown_table)
    print()
    
    # 2. 演示数据知识库信息
    print("2️⃣ 数据知识库信息（AI会获得的上下文）:")
    print("-" * 30)
    
    # 模拟知识库构建
    basic_info = f"当前数据集: demo_data.xlsx\n- 行数: {demo_data.shape[0]}, 列数: {demo_data.shape[1]}"
    
    col_info = "\n- 列信息详情:"
    for i, col in enumerate(demo_data.columns):
        dtype = demo_data[col].dtype
        non_null_count = demo_data[col].count()
        null_count = len(demo_data) - non_null_count
        
        sample_values = demo_data[col].dropna().head(3).tolist()
        sample_str = ', '.join([str(v)[:20] for v in sample_values])
        
        col_info += f"\n  {i+1}. '{col}' ({dtype}) - 非空值:{non_null_count}, 缺失值:{null_count}, 示例:[{sample_str}]"
    
    sample_data = "\n- 前5行数据样本:\n"
    sample_data += demo_data.head(5).to_string(max_cols=10, max_colwidth=20)
    
    stats_info = "\n- 数据统计摘要:"
    numeric_cols = demo_data.select_dtypes(include=['number']).columns
    if len(numeric_cols) > 0:
        stats_info += f"\n  数值列({len(numeric_cols)}个): {', '.join(numeric_cols)}"
    
    text_cols = demo_data.select_dtypes(include=['object', 'string']).columns
    if len(text_cols) > 0:
        stats_info += f"\n  文本列({len(text_cols)}个): {', '.join(text_cols)}"
    
    knowledge_base = f"{basic_info}{col_info}{sample_data}{stats_info}"
    print(knowledge_base)
    print()
    
    # 3. 演示自动重试机制
    print("3️⃣ 自动重试机制演示:")
    print("-" * 30)
    print("当AI生成的代码执行失败时，系统会:")
    print("• 显示错误信息")
    print("• 自动向AI请求修复代码")
    print("• 最多重试3次")
    print("• 显示完整的重试过程")
    print()
    
    # 模拟重试过程
    print("模拟重试过程:")
    print("❌ 第1次执行失败: KeyError: '销售额' (列名不存在)")
    print("🔄 正在尝试第1次自动重试，向AI请求修复代码...")
    print("🤖 AI修复建议: 将'销售额'改为'销量'")
    print("✅ 第2次执行成功!")
    print()
    
    # 4. 演示streamlit集成
    print("4️⃣ Streamlit集成功能:")
    print("-" * 30)
    print("• 数据预览使用streamlit显示")
    print("• 支持markdown格式展示")
    print("• 提供数据下载功能")
    print("• 交互式数据表格")
    print("• 自动在浏览器中打开")
    print()
    
    # 5. 演示AI对话改进
    print("5️⃣ AI对话改进:")
    print("-" * 30)
    print("• 合并了预览和结果预览按钮")
    print("• AI获得更详细的数据上下文")
    print("• 提高了代码生成准确率")
    print("• 自动处理常见错误")
    print()
    
    return True

def show_usage_examples():
    """显示使用示例"""
    print("📖 使用示例:")
    print("=" * 50)
    
    examples = [
        {
            "场景": "数据筛选",
            "用户输入": "找出价格大于5000的产品",
            "AI知识库": "知道有'价格'列，数据类型为int64，示例值[7999, 6999, 3999]",
            "生成代码": "result = self.current_df[self.current_df['价格'] > 5000]",
            "自动重试": "如果列名错误，自动修正为正确的列名"
        },
        {
            "场景": "数据统计",
            "用户输入": "计算各品牌的平均评分",
            "AI知识库": "知道有'评分'列，数据类型为float64，示例值[4.8, 4.6, 4.7]",
            "生成代码": "result = self.current_df.groupby('产品名称')['评分'].mean()",
            "自动重试": "如果分组列不存在，自动建议相似列名"
        },
        {
            "场景": "数据可视化",
            "用户输入": "创建价格和销量的散点图",
            "AI知识库": "知道有'价格'和'销量'列，了解数据范围和类型",
            "生成代码": "plt.scatter(self.current_df['价格'], self.current_df['销量'])",
            "自动重试": "如果绘图失败，自动添加必要的导入和配置"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"{i}. {example['场景']}:")
        for key, value in example.items():
            if key != '场景':
                print(f"   {key}: {value}")
        print()

if __name__ == "__main__":
    print("🚀 local_ai_fixedV0.8.py 新功能演示")
    print("=" * 60)
    
    try:
        demo_new_features()
        show_usage_examples()
        
        print("🎉 演示完成!")
        print("\n💡 主要改进总结:")
        print("1. 修复了文件保存对话框参数错误")
        print("2. 增强了数据预览的markdown格式显示")
        print("3. 大幅提升了AI代码生成准确率（通过详细知识库）")
        print("4. 实现了智能错误重试机制")
        print("5. 完善了streamlit数据展示功能")
        print("6. 优化了用户交互体验")
        
        print("\n🔧 技术特点:")
        print("• 自动错误检测和修复")
        print("• 智能列名匹配和建议")
        print("• 详细的数据上下文提供")
        print("• 完整的重试过程展示")
        print("• 高质量的数据可视化")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
    
    print("\n" + "=" * 60)
    input("按回车键退出...")
