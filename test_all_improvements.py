# -*- coding: utf-8 -*-
"""
测试所有改进功能
"""
import pandas as pd
import os

def test_all_improvements():
    """测试所有改进功能"""
    print("🔍 测试所有改进功能...")
    
    try:
        # 测试代码导入
        with open('local_ai_fixedV0.8.py', 'r', encoding='utf-8') as f:
            code_content = f.read()
        
        print(f"✅ 代码文件读取成功，长度: {len(code_content)} 字符")
        
        # 1. 测试文件保存对话框修复
        print("\n1. 测试文件保存对话框修复:")
        if 'initialfilename' in code_content:
            print("❌ 仍然存在错误的 initialfilename 参数")
        else:
            print("✅ 文件保存对话框参数已修复")
        
        # 2. 测试markdown格式转换
        print("\n2. 测试markdown格式转换:")
        if 'to_markdown' in code_content and 'markdown_table' in code_content:
            print("✅ 已添加markdown格式转换功能")
        else:
            print("❌ markdown格式转换功能未找到")
        
        # 3. 测试数据知识库功能
        print("\n3. 测试数据知识库功能:")
        if '_build_data_knowledge_base' in code_content:
            print("✅ 已添加数据知识库构建功能")
            if '前5行数据样本' in code_content and '列信息详情' in code_content:
                print("✅ 知识库包含详细的数据信息")
            else:
                print("⚠️ 知识库信息可能不完整")
        else:
            print("❌ 数据知识库功能未找到")
        
        # 4. 测试自动重试功能
        print("\n4. 测试自动重试功能:")
        if '_execute_code_with_retry' in code_content:
            print("✅ 已添加自动重试功能")
            if 'max_retries=3' in code_content:
                print("✅ 最大重试次数设置为3次")
            if '_build_retry_prompt' in code_content:
                print("✅ 已添加重试提示构建功能")
            if '正在尝试第' in code_content and '次自动重试' in code_content:
                print("✅ 包含重试过程显示")
        else:
            print("❌ 自动重试功能未找到")
        
        # 5. 测试streamlit集成
        print("\n5. 测试streamlit集成:")
        if 'streamlit' in code_content and '_launch_streamlit_preview' in code_content:
            print("✅ streamlit集成功能存在")
            if 'markdown_data' in code_content and 'download_button' in code_content:
                print("✅ 包含markdown下载功能")
        else:
            print("❌ streamlit集成功能不完整")
        
        # 6. 测试AI预览合并功能
        print("\n6. 测试AI预览合并功能:")
        if '_preview_ai_operation_with_streamlit' in code_content:
            print("✅ AI预览合并功能存在")
            # 检查是否移除了结果预览按钮
            result_preview_count = code_content.count('结果预览')
            if result_preview_count < 3:  # 应该大幅减少
                print("✅ 结果预览按钮已大幅减少")
            else:
                print(f"⚠️ 仍有 {result_preview_count} 个结果预览引用")
        else:
            print("❌ AI预览合并功能未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 测试基本功能:")
    
    try:
        # 创建测试数据
        test_data = pd.DataFrame({
            '姓名': ['张三', '李四', '王五'],
            '年龄': [25, 30, 35],
            '城市': ['北京', '上海', '广州'],
            '薪资': [8000, 12000, 15000]
        })
        print("✅ 测试数据创建成功")
        
        # 测试markdown转换
        markdown_result = test_data.to_markdown(index=False)
        print("✅ Markdown转换功能正常")
        print(f"Markdown示例:\n{markdown_result[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 开始测试所有改进功能")
    print("=" * 60)
    
    success1 = test_all_improvements()
    success2 = test_basic_functionality()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 所有改进功能测试通过！")
        print("\n📋 功能总结:")
        print("1. ✅ 修复了文件保存对话框的参数错误")
        print("2. ✅ 添加了数据markdown格式转换和下载")
        print("3. ✅ 增强了AI知识库，包含表头和前5行数据")
        print("4. ✅ 实现了AI对话自动重试机制（最多3次）")
        print("5. ✅ 完善了streamlit数据预览功能")
        print("6. ✅ 合并了AI对话中的预览按钮")
        
        print("\n🔧 新功能说明:")
        print("• 文件保存：修复了另存为对话框的参数错误")
        print("• Markdown显示：数据在streamlit中以markdown格式展示")
        print("• AI知识库：包含详细的列信息、数据类型、示例值")
        print("• 自动重试：代码执行失败时自动向AI请求修复，最多重试3次")
        print("• 过程显示：完整显示重试过程和AI修复建议")
    else:
        print("❌ 部分功能测试失败，请检查代码")
    
    print("=" * 60)
    input("\n按回车键退出...")
