# -*- coding: utf-8 -*-
"""
测试groupby列名检测
"""
import re

def test_groupby_detection():
    """测试groupby列名检测"""
    print("🔍 测试groupby列名检测...")
    
    test_codes = [
        "result = self.current_df.groupby('部门').mean()",
        "result = self.current_df.groupby(['部门', '职位']).sum()",
        "result = self.current_df.drop(columns=['姓名'])",
        "result = self.current_df[self.current_df['年龄'] > 30]"
    ]
    
    for i, code in enumerate(test_codes, 1):
        print(f"\n{i}. 测试代码: {code}")
        
        # 使用修复后的列名检测逻辑
        column_patterns = [
            r"self\.current_df\[(['\"])([^'\"]+)\1\]",  # self.current_df['column']
            r"columns\s*=\s*\[([^\]]+)\]",  # columns=['col1', 'col2']
            r"drop\s*\(\s*columns\s*=\s*\[([^\]]+)\]",  # drop(columns=['col'])
            r"groupby\s*\(\s*(['\"])([^'\"]+)\1\s*\)",  # groupby('column')
            r"groupby\s*\(\s*\[([^\]]+)\]\s*\)",  # groupby(['col1', 'col2'])
        ]

        found_columns = set()
        for pattern in column_patterns:
            matches = re.findall(pattern, code)
            if r"self\.current_df\[" in pattern:  # 括号访问
                found_columns.update([match[1] for match in matches])
            elif "columns" in pattern:  # columns参数中的列名
                for match in matches:
                    # 提取引号内的列名
                    col_matches = re.findall(r"['\"]([^'\"]+)['\"]", match)
                    found_columns.update(col_matches)
            elif "groupby" in pattern:  # groupby参数中的列名
                for match in matches:
                    if isinstance(match, tuple) and len(match) >= 2:
                        # groupby('column') 格式
                        found_columns.add(match[1])
                    elif isinstance(match, str):
                        # groupby(['col1', 'col2']) 格式
                        col_matches = re.findall(r"['\"]([^'\"]+)['\"]", match)
                        found_columns.update(col_matches)

        # 排除pandas方法和属性
        pandas_methods = ['shape', 'columns', 'index', 'dtypes', 'head', 'tail', 'info', 'describe', 
                         'drop', 'groupby', 'sort_values', 'fillna', 'dropna', 'reset_index',
                         'loc', 'iloc', 'at', 'iat', 'query', 'eval', 'apply', 'map', 'transform']
        
        # 过滤掉pandas方法
        actual_columns = [col for col in found_columns if col not in pandas_methods and len(col) > 0]
        
        print(f"   检测到的列名: {actual_columns}")
        
        # 验证结果
        if i == 1:  # groupby('部门')
            expected = ['部门']
            if actual_columns == expected:
                print("   ✅ 正确检测到groupby列名")
            else:
                print(f"   ❌ 检测错误，期望: {expected}, 实际: {actual_columns}")
        elif i == 2:  # groupby(['部门', '职位'])
            expected = set(['部门', '职位'])
            if set(actual_columns) == expected:
                print("   ✅ 正确检测到多个groupby列名")
            else:
                print(f"   ❌ 检测错误，期望: {expected}, 实际: {set(actual_columns)}")
        elif i == 3:  # drop(columns=['姓名'])
            expected = ['姓名']
            if actual_columns == expected:
                print("   ✅ 正确检测到drop列名")
            else:
                print(f"   ❌ 检测错误，期望: {expected}, 实际: {actual_columns}")
        elif i == 4:  # self.current_df['年龄']
            expected = ['年龄']
            if actual_columns == expected:
                print("   ✅ 正确检测到索引列名")
            else:
                print(f"   ❌ 检测错误，期望: {expected}, 实际: {actual_columns}")

def test_comprehensive_scenarios():
    """测试综合场景"""
    print("\n🔍 测试综合场景...")
    
    scenarios = [
        {
            "name": "复杂的groupby操作",
            "code": "result = self.current_df.groupby(['部门', '职位'])[['工资', '奖金']].mean()",
            "expected": ['部门', '职位', '工资', '奖金']
        },
        {
            "name": "链式操作",
            "code": "result = self.current_df[self.current_df['年龄'] > 30].groupby('部门').sum()",
            "expected": ['年龄', '部门']
        },
        {
            "name": "多种操作组合",
            "code": "temp = self.current_df.drop(columns=['备注']); result = temp.groupby('类别').mean()",
            "expected": ['备注', '类别']
        }
    ]
    
    for scenario in scenarios:
        print(f"\n场景: {scenario['name']}")
        print(f"代码: {scenario['code']}")
        
        # 列名检测逻辑
        column_patterns = [
            r"self\.current_df\[(['\"])([^'\"]+)\1\]",
            r"columns\s*=\s*\[([^\]]+)\]",
            r"drop\s*\(\s*columns\s*=\s*\[([^\]]+)\]",
            r"groupby\s*\(\s*(['\"])([^'\"]+)\1\s*\)",
            r"groupby\s*\(\s*\[([^\]]+)\]\s*\)",
            r"\[([^\]]+)\]\.mean\(\)",  # 额外的列选择模式
        ]

        found_columns = set()
        for pattern in column_patterns:
            matches = re.findall(pattern, scenario['code'])
            if r"self\.current_df\[" in pattern:
                found_columns.update([match[1] for match in matches])
            elif "columns" in pattern or "mean" in pattern:
                for match in matches:
                    col_matches = re.findall(r"['\"]([^'\"]+)['\"]", match)
                    found_columns.update(col_matches)
            elif "groupby" in pattern:
                for match in matches:
                    if isinstance(match, tuple) and len(match) >= 2:
                        found_columns.add(match[1])
                    elif isinstance(match, str):
                        col_matches = re.findall(r"['\"]([^'\"]+)['\"]", match)
                        found_columns.update(col_matches)

        pandas_methods = ['drop', 'groupby', 'mean', 'sum', 'count']
        actual_columns = [col for col in found_columns if col not in pandas_methods]
        
        print(f"检测到的列名: {actual_columns}")
        
        # 检查是否包含期望的列名
        expected_set = set(scenario['expected'])
        actual_set = set(actual_columns)
        
        if expected_set.issubset(actual_set):
            print("✅ 包含了所有期望的列名")
        else:
            missing = expected_set - actual_set
            print(f"⚠️ 缺少列名: {missing}")

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 测试groupby列名检测")
    print("=" * 60)
    
    test_groupby_detection()
    test_comprehensive_scenarios()
    
    print("\n" + "=" * 60)
    print("📋 总结")
    print("=" * 60)
    print("✅ 修复内容:")
    print("• 添加了groupby('column')模式的检测")
    print("• 添加了groupby(['col1', 'col2'])模式的检测")
    print("• 保持了原有的drop和索引访问检测")
    print("• 正确排除了pandas方法名")
    
    print("\n🔧 现在支持检测:")
    print("• self.current_df['列名'] - 索引访问")
    print("• drop(columns=['列名']) - 删除列")
    print("• groupby('列名') - 单列分组")
    print("• groupby(['列1', '列2']) - 多列分组")
    
    print("=" * 60)
    input("\n按回车键退出...")
