# -*- coding: utf-8 -*-
"""
测试markdown修复
"""
import pandas as pd
import sys
import os

# 添加当前目录到路径
sys.path.append('.')

def test_safe_to_markdown():
    """测试安全的markdown转换函数"""
    print("🔍 测试安全的markdown转换...")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '序号': [1, 2, 3],
        '姓名': ['张三', '李四', '王五'],
        '年龄': [25, 30, 35],
        '城市': ['北京', '上海', '广州']
    })
    
    print("测试数据:")
    print(test_data.to_string(index=False))
    print()
    
    # 模拟_safe_to_markdown函数
    def safe_to_markdown(df):
        """安全地将DataFrame转换为markdown格式，处理tabulate依赖问题"""
        try:
            return df.to_markdown(index=False)
        except ImportError:
            # 如果缺少tabulate依赖，创建简化的markdown表格
            headers = "| " + " | ".join(df.columns) + " |"
            separator = "|" + "|".join([" --- " for _ in df.columns]) + "|"
            rows = []
            for _, row in df.iterrows():
                row_str = "| " + " | ".join(str(val) for val in row) + " |"
                rows.append(row_str)
            return "\n".join([headers, separator] + rows)
        except Exception as e:
            return f"Markdown转换失败: {str(e)}"
    
    # 测试转换
    print("1. 测试markdown转换:")
    markdown_result = safe_to_markdown(test_data)
    print("转换结果:")
    print("-" * 50)
    print(markdown_result)
    print("-" * 50)
    
    # 检查结果
    if "Markdown转换失败" in markdown_result:
        print("❌ 转换失败")
        return False
    elif "|" in markdown_result and "---" in markdown_result:
        print("✅ 转换成功，生成了有效的markdown表格")
        return True
    else:
        print("⚠️ 转换结果格式异常")
        return False

def test_tabulate_availability():
    """测试tabulate库的可用性"""
    print("\n🔍 测试tabulate库可用性...")
    
    try:
        import tabulate
        print("✅ tabulate库已安装")
        
        # 测试pandas的to_markdown方法
        test_df = pd.DataFrame({'A': [1, 2], 'B': ['x', 'y']})
        markdown = test_df.to_markdown(index=False)
        print("✅ pandas.to_markdown()方法正常工作")
        print("示例输出:")
        print(markdown)
        return True
        
    except ImportError:
        print("❌ tabulate库未安装")
        print("💡 可以通过以下命令安装: pip install tabulate")
        
        # 测试备用方案
        print("\n测试备用方案:")
        test_df = pd.DataFrame({'A': [1, 2], 'B': ['x', 'y']})
        try:
            markdown = test_df.to_markdown(index=False)
            print("⚠️ 意外：to_markdown()方法仍然工作")
        except ImportError:
            print("✅ 正确触发ImportError，将使用备用方案")
            # 创建简化的markdown表格
            headers = "| " + " | ".join(test_df.columns) + " |"
            separator = "|" + "|".join([" --- " for _ in test_df.columns]) + "|"
            rows = []
            for _, row in test_df.iterrows():
                row_str = "| " + " | ".join(str(val) for val in row) + " |"
                rows.append(row_str)
            backup_markdown = "\n".join([headers, separator] + rows)
            print("备用方案输出:")
            print(backup_markdown)
        
        return False

def test_streamlit_integration():
    """测试streamlit集成"""
    print("\n🔍 测试streamlit集成...")
    
    # 模拟streamlit环境
    class MockStreamlit:
        def write(self, text):
            print(f"st.write: {text}")
        
        def markdown(self, text):
            print(f"st.markdown: {text[:100]}...")
        
        def error(self, text):
            print(f"st.error: {text}")
        
        def info(self, text):
            print(f"st.info: {text}")
        
        def warning(self, text):
            print(f"st.warning: {text}")
    
    st = MockStreamlit()
    
    # 测试数据
    display_df = pd.DataFrame({
        '列1': ['值1', '值2'],
        '列2': ['值3', '值4']
    })
    
    print("模拟streamlit显示过程:")
    
    # 模拟修复后的代码逻辑
    def safe_to_markdown(df):
        try:
            return df.to_markdown(index=False)
        except ImportError:
            headers = "| " + " | ".join(df.columns) + " |"
            separator = "|" + "|".join([" --- " for _ in df.columns]) + "|"
            rows = []
            for _, row in df.iterrows():
                row_str = "| " + " | ".join(str(val) for val in row) + " |"
                rows.append(row_str)
            return "\n".join([headers, separator] + rows)
        except Exception as e:
            return f"Markdown转换失败: {str(e)}"
    
    # 显示markdown格式的数据表格
    st.write("**Markdown格式数据表格：**")
    markdown_table = safe_to_markdown(display_df)
    if "Markdown转换失败" in markdown_table:
        st.error(markdown_table)
        st.info("💡 建议安装tabulate库: pip install tabulate")
    else:
        st.markdown("```markdown\\n" + markdown_table + "\\n```")
    
    print("✅ streamlit集成测试完成")
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 测试markdown修复")
    print("=" * 60)
    
    success1 = test_safe_to_markdown()
    success2 = test_tabulate_availability()
    success3 = test_streamlit_integration()
    
    print("\n" + "=" * 60)
    if success1 and success3:
        print("🎉 Markdown修复测试通过！")
        print("\n✅ 修复总结:")
        print("• 添加了_safe_to_markdown()安全转换函数")
        print("• 处理了tabulate依赖缺失的情况")
        print("• 提供了简化的markdown表格备用方案")
        print("• 改进了错误提示和用户指导")
        print("• 统一了markdown转换逻辑")
        
        if not success2:
            print("\n⚠️ 注意:")
            print("• tabulate库未安装，将使用简化的markdown格式")
            print("• 建议运行: pip install tabulate 获得更好的格式")
        
        print("\n🔧 现在支持的功能:")
        print("• 自动检测tabulate库可用性")
        print("• 智能降级到简化markdown格式")
        print("• 清晰的错误提示和安装建议")
        print("• 统一的markdown转换接口")
    else:
        print("❌ 部分测试失败")
    
    print("=" * 60)
    input("\n按回车键退出...")
