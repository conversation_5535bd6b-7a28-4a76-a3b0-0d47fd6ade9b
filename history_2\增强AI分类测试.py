#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强AI分类功能测试脚本
测试规则引擎、多轮验证、置信度评估等新功能
"""

import pandas as pd
import json
import os

def create_enhanced_test_data():
    """创建增强测试数据，包含更多专业术语和复杂场景"""
    test_problems = [
        # 元器件相关问题（测试元器件识别）
        "电容C15耐压不足，在高温环境下发生击穿，导致电路短路",
        "MOSFET Q3选型错误，导通电阻过大，功耗超标引起过热",
        "集成电路IC2内部ESD保护不足，静电放电导致器件损坏",
        "LED显示屏亮度不均匀，部分LED老化失效",
        "传感器精度不满足要求，温度漂移过大",
        
        # 工艺相关问题（测试工艺术语识别）
        "回流焊温度曲线设置不当，导致焊点虚焊",
        "波峰焊助焊剂残留过多，影响绝缘性能",
        "PCB表面镀金层厚度不均，影响焊接质量",
        "丝印位置偏移，元器件标识不清",
        "贴片工艺参数错误，元器件位置偏移",
        
        # 软件相关问题
        "软件模块间接口协议定义不一致，数据传输异常",
        "算法逻辑错误，边界条件处理不当",
        "代码版本管理混乱，集成时出现冲突",
        "单元测试覆盖率不足，未发现潜在缺陷",
        
        # 设计相关问题
        "电路设计冗余不足，单点故障导致系统失效",
        "EMC设计考虑不周，电磁干扰超标",
        "热设计不合理，散热不良导致器件过热",
        "接口设计不兼容，与外部设备连接异常",
        
        # 管理相关问题
        "操作人员培训不到位，违规操作导致产品损坏",
        "质量检验制度执行不严，不良品流出",
        "工艺文件描述不清晰，操作人员理解错误",
        "责任制不落实，质量问题无人负责",
        
        # 外协外购件问题
        "外购印制板设计缺陷，走线宽度不满足载流要求",
        "外协加工精度不够，装配间隙过大",
        "供应商质量管控不严，来料检验发现批次性问题",
        
        # 使用环境问题
        "用户超规格使用，输入电压超出额定范围",
        "外部电磁干扰导致设备误动作",
        "环境温湿度超出设计范围，设备性能下降",
        
        # 复杂混合问题
        "需求分析阶段对EMC要求理解不准确，导致后续设计缺陷",
        "工艺工装设计错误，装配精度不够，影响产品性能",
        "元器件供应商变更未充分验证，新批次器件参数偏差大"
    ]
    
    # 对应的期望分类结果
    expected_results = [
        "功能性能和物理特性设计",  # 电容耐压设计问题
        "功能性能和物理特性设计",  # MOSFET选型设计问题
        "功能性能和物理特性设计",  # IC ESD设计问题
        "元器件偶发失效",          # LED老化
        "元器件设计",              # 传感器精度设计
        
        "工艺设计",                # 回流焊工艺
        "工艺设计",                # 波峰焊工艺
        "工艺设计",                # 镀金工艺
        "工艺设计",                # 丝印工艺
        "工艺设计",                # 贴片工艺
        
        "软件设计",                # 接口协议
        "软件设计",                # 算法逻辑
        "软件管理",                # 版本管理
        "软件测试",                # 测试覆盖
        
        "功能性能和物理特性设计",  # 冗余设计
        "通用质量特性设计",        # EMC设计
        "功能性能和物理特性设计",  # 热设计
        "接口设计",                # 接口兼容性
        
        "培训",                    # 人员培训
        "责任制不落实",            # 检验制度
        "工艺文件可操作性",        # 工艺文件
        "责任制不落实",            # 责任制
        
        "外协外购件设计",          # 外购PCB设计
        "外协外购件工艺",          # 外协加工精度
        "外协外购件生产管理",      # 供应商管控
        
        "用户使用不当",            # 超规格使用
        "外部环境",                # 电磁干扰
        "外部环境",                # 环境条件
        
        "需求分析",                # 需求理解错误
        "工艺工装设计",            # 工装设计
        "元器件准入"               # 供应商变更验证
    ]
    
    # 确保数组长度一致
    num_problems = len(test_problems)
    severity_pattern = ['高', '中', '低'] * (num_problems // 3 + 1)
    
    df = pd.DataFrame({
        '问题描述': test_problems,
        '期望分类': expected_results,
        '问题编号': [f'EP{i+1:03d}' for i in range(num_problems)],  # Enhanced Problem
        '严重程度': severity_pattern[:num_problems],
        '问题类型': ['质量问题'] * num_problems
    })
    
    return df

def analyze_enhanced_results(df_result):
    """分析增强功能的效果"""
    if 'AI分类结果' not in df_result.columns:
        print("缺少AI分类结果列")
        return
    
    print("\n=== 增强功能效果分析 ===")
    
    # 1. 置信度分析
    low_confidence = df_result[df_result['AI分类结果'].str.contains('低置信度', na=False)]
    medium_confidence = df_result[df_result['AI分类结果'].str.contains('中置信度', na=False)]
    rule_based = df_result[df_result['AI分类结果'].str.contains('规则', na=False)]
    
    print(f"置信度分布:")
    print(f"  低置信度结果: {len(low_confidence)}个 ({len(low_confidence)/len(df_result)*100:.1f}%)")
    print(f"  中置信度结果: {len(medium_confidence)}个 ({len(medium_confidence)/len(df_result)*100:.1f}%)")
    print(f"  规则引擎预测: {len(rule_based)}个 ({len(rule_based)/len(df_result)*100:.1f}%)")
    
    # 2. 专业术语识别效果
    component_problems = df_result[df_result['问题描述'].str.contains('电容|MOSFET|IC|LED|传感器', na=False)]
    process_problems = df_result[df_result['问题描述'].str.contains('焊|镀|贴片|丝印', na=False)]
    
    print(f"\n专业术语识别:")
    print(f"  包含元器件术语的问题: {len(component_problems)}个")
    print(f"  包含工艺术语的问题: {len(process_problems)}个")
    
    # 3. 分类准确性（如果有期望结果）
    if '期望分类' in df_result.columns:
        # 清理分类结果，移除置信度标记
        df_result['清理后分类'] = df_result['AI分类结果'].str.replace(r'\[.*?\]', '', regex=True)
        
        correct = sum(df_result['清理后分类'] == df_result['期望分类'])
        total = len(df_result)
        accuracy = correct / total * 100
        
        print(f"\n分类准确性:")
        print(f"  总样本数: {total}")
        print(f"  正确分类: {correct}")
        print(f"  准确率: {accuracy:.1f}%")
        
        # 分析错误分类
        errors = df_result[df_result['清理后分类'] != df_result['期望分类']]
        if len(errors) > 0:
            print(f"\n错误分类分析 ({len(errors)}个):")
            for idx, row in errors.head(5).iterrows():  # 只显示前5个错误
                print(f"  问题: {row['问题描述'][:40]}...")
                print(f"    期望: {row['期望分类']}")
                print(f"    实际: {row['AI分类结果']}")
                print()

def generate_enhancement_report(df_result):
    """生成增强功能报告"""
    print("\n=== 增强功能使用报告 ===")
    
    # 统计各种增强功能的使用情况
    total_count = len(df_result)
    
    # 规则引擎使用统计
    rule_predictions = df_result[df_result['AI分类结果'].str.contains('规则', na=False)]
    print(f"规则引擎预测: {len(rule_predictions)}个 ({len(rule_predictions)/total_count*100:.1f}%)")
    
    # 置信度分布
    confidence_stats = {
        '高置信度': len(df_result) - len(df_result[df_result['AI分类结果'].str.contains('置信度', na=False)]),
        '中置信度': len(df_result[df_result['AI分类结果'].str.contains('中置信度', na=False)]),
        '低置信度': len(df_result[df_result['AI分类结果'].str.contains('低置信度', na=False)])
    }
    
    print(f"\n置信度分布:")
    for level, count in confidence_stats.items():
        percentage = count / total_count * 100
        print(f"  {level}: {count}个 ({percentage:.1f}%)")
    
    # 建议
    low_conf_count = confidence_stats['低置信度']
    if low_conf_count > 0:
        print(f"\n建议:")
        print(f"  - 有{low_conf_count}个低置信度结果需要人工复核")
        print(f"  - 建议优先检查包含专业术语但分类置信度低的问题")
        print(f"  - 可以考虑补充更多专业知识库内容")

def main():
    """主函数"""
    print("=== 增强AI分类功能测试 ===")
    
    # 创建增强测试数据
    print("1. 创建增强测试数据...")
    df_test = create_enhanced_test_data()
    
    # 保存测试数据
    test_file = "增强AI分类测试数据.csv"
    df_test.to_csv(test_file, index=False, encoding='utf-8-sig')
    print(f"增强测试数据已保存到: {test_file}")
    
    print(f"\n2. 测试数据概览:")
    print(f"总问题数: {len(df_test)}")
    print(f"包含元器件术语: {len(df_test[df_test['问题描述'].str.contains('电容|MOSFET|IC|LED|传感器', na=False)])}个")
    print(f"包含工艺术语: {len(df_test[df_test['问题描述'].str.contains('焊|镀|贴片|丝印', na=False)])}个")
    print(f"包含软件术语: {len(df_test[df_test['问题描述'].str.contains('软件|代码|算法|接口', na=False)])}个")
    
    print(f"\n3. 新功能测试要点:")
    print("✓ 规则引擎预分类 - 基于关键词自动识别")
    print("✓ 专业知识库增强 - 元器件和工艺术语识别")
    print("✓ 多轮AI验证 - 提高分类一致性")
    print("✓ 置信度评估 - 标识需要人工复核的结果")
    print("✓ 结果验证 - 自动检查分类合理性")
    
    print(f"\n4. 使用说明:")
    print("请在主程序中执行以下步骤:")
    print(f"1) 加载文件: {test_file}")
    print("2) 选择列: 问题描述")
    print("3) 选择配置: 问题原因分类")
    print("4) 执行AI智能分类")
    print("5) 观察结果中的置信度标记和规则预测标记")
    
    print(f"\n5. 结果分析:")
    print("分类完成后，请运行以下代码分析增强功能效果:")
    print("""
# 加载分类结果
df_result = pd.read_csv('result.csv', encoding='utf-8-sig')

# 分析增强功能效果
analyze_enhanced_results(df_result)
generate_enhancement_report(df_result)
""")

if __name__ == "__main__":
    main()
