# -*- coding: utf-8 -*-
"""
使用pandas直接读取Excel或CSV文件，通过图形界面进行数据处理
"""
import os
import sys
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
import tkinter as tk
from tkinter import ttk, filedialog, scrolledtext, messagebox
import matplotlib
import traceback
import re
import io  # 用于处理字节流
from importlib import util  # 用于检查模块是否可导入
import requests  # 用于与Ollama API通信
import json  # 用于解析Ollama API响应
from difflib import SequenceMatcher  # 用于计算字符串相似度
import streamlit as st
import subprocess
import threading
import time
import webbrowser
import tempfile

# 设置matplotlib支持中文
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 用黑体显示中文
matplotlib.rcParams['axes.unicode_minus'] = False    # 正常显示负号

# 配置文件路径
try:
    CONFIG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "local_excel_config.json")
except NameError:
    # 如果__file__未定义（如在exec中运行），使用当前目录
    CONFIG_FILE = os.path.join(os.getcwd(), "local_excel_config.json")

# Ollama服务器和模型的全局变量
OLLAMA_SERVER = "http://localhost:11434"
OLLAMA_MODEL = "qwen2.5-coder:7b"

# 默认配置
DEFAULT_CONFIG = {
    "ollama_server": OLLAMA_SERVER,
    "ollama_model": OLLAMA_MODEL,
    "last_dir": "",
    "system_prompt": "你是一个专业的数据分析助手，专门帮助用户使用pandas处理Excel/CSV数据。请严格遵循以下规则：\n\n【代码规范】\n1. 必须使用 self.current_df 作为数据源变量名，不要使用df、data等其他名称\n2. 将最终结果赋值给变量 result（如果是DataFrame操作）\n3. 代码必须用```python和```包裹\n4. 确保代码可以直接运行，无需额外导入\n\n【错误处理】\n1. 在访问列之前检查列是否存在：if 'column_name' in self.current_df.columns\n2. 在使用索引前检查数据是否为空：if not self.current_df.empty\n3. 处理数据类型转换错误：使用pd.to_numeric(errors='coerce')\n4. 避免直接使用.iloc[0]或.loc[0]，先检查索引范围\n\n【常见操作模式】\n1. 筛选数据：result = self.current_df[self.current_df['列名'] == '条件']\n2. 分组统计：result = self.current_df.groupby('列名').agg({'目标列': '统计函数'})\n3. 排序：result = self.current_df.sort_values('列名', ascending=False)\n4. 新增列：self.current_df['新列'] = 计算表达式; result = self.current_df\n5. 删除行/列：result = self.current_df.drop(columns=['列名']) 或 result = self.current_df.drop(index=索引)\n\n【数据验证】\n1. 操作前检查数据形状和类型\n2. 操作后验证结果的合理性\n3. 提供清晰的操作说明和结果解释\n\n请根据用户的自然语言描述，生成安全、准确、可执行的pandas代码。"
}

# 加载配置
def load_config():
    """加载配置文件，如果不存在则创建默认配置"""
    global OLLAMA_SERVER, OLLAMA_MODEL
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 更新全局变量
                OLLAMA_SERVER = config.get("ollama_server", OLLAMA_SERVER)
                OLLAMA_MODEL = config.get("ollama_model", OLLAMA_MODEL)
                return config
        except:
            return DEFAULT_CONFIG.copy()
    else:
        # 创建默认配置文件
        save_config(DEFAULT_CONFIG)
        return DEFAULT_CONFIG.copy()

# 保存配置
def save_config(config):
    """保存配置到文件"""
    global OLLAMA_SERVER, OLLAMA_MODEL
    # 确保全局变量与配置同步
    OLLAMA_SERVER = config.get("ollama_server", OLLAMA_SERVER)
    OLLAMA_MODEL = config.get("ollama_model", OLLAMA_MODEL)
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return True
    except:
        return False

# Ollama大模型交互类
class OllamaClient:
    """与本地Ollama大模型通信的客户端"""
    def __init__(self, base_url=None, model=None):
        self.base_url = base_url or OLLAMA_SERVER  # 使用全局变量
        self.model = model or OLLAMA_MODEL  # 使用全局变量
        self.api_url = f"{self.base_url}/api/generate"
        self.available = self._check_availability()
        
    def _check_availability(self):
        """检查Ollama服务是否可用"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=2)
            return response.status_code == 200
        except Exception as e:
            print(f"Ollama服务连接失败: {e}")
            return False
            
    def get_models(self):
        """获取可用模型列表"""
        try:
            response = requests.get(f"{self.base_url}/api/tags")
            if response.status_code == 200:
                data = response.json()
                # 修正解析方式，兼容不同版本的Ollama API
                if 'models' in data:
                    return [model['name'] for model in data.get('models', [])]
                elif 'models' in data:
                    return data['models']
                else:
                    return [model['name'] for model in data]
            return []
        except Exception as e:
            print(f"获取模型列表失败: {e}")
            return []
    
    def ask(self, prompt, system_prompt=None, context=None):
        """向模型提问"""
        if not self.available:
            return "错误: 无法连接到Ollama服务。请确保Ollama已在本地启动。"
            
        payload = {
            "model": self.model,
            "prompt": prompt,
            "stream": False
        }
        
        if system_prompt:
            payload["system"] = system_prompt
            
        if context:
            payload["context"] = context
            
        try:
            # 添加超时设置，避免无限等待
            response = requests.post(self.api_url, json=payload, timeout=60)
            if response.status_code == 200:
                data = response.json()
                return data.get('response', '无回应')
            else:
                return f"错误: API请求失败，状态码 {response.status_code}，响应内容: {response.text}"
        except requests.exceptions.Timeout:
            return "错误: 请求超时，请检查模型是否正在加载或者是否选择了过大的模型"
        except requests.exceptions.ConnectionError:
            self.available = False
            return "错误: 连接失败，Ollama服务可能已停止运行"
        except Exception as e:
            return f"错误: {str(e)}"
        
    def set_model(self, model_name):
        """设置使用的模型"""
        global OLLAMA_MODEL
        self.model = model_name
        OLLAMA_MODEL = model_name  # 更新全局变量

# 条件导入特定平台所需模块
if sys.platform == 'win32':
    has_win32clipboard = util.find_spec("win32clipboard") is not None
    has_pil = util.find_spec("PIL") is not None
    if has_win32clipboard and has_pil:
        try:
            import win32clipboard
            from PIL import Image
        except ImportError:
            print("警告: 虽然检测到pywin32和PIL，但导入失败，部分图表复制功能可能不可用")

class ExcelSimpleApp:
    """Excel/CSV数据处理主类"""
    def __init__(self, root):
        self.root = root
        self.root.title("Excel/CSV数据助手")
        self.current_df = None
        self.current_path = None
        self.current_sheet = None  # 当前选择的工作表名称
        self.current_figure = None
        self.canvas = None
        self._file_modified = False
        self._original_df = None
        self.result_frame = None
        self.viz_frame = None
        self.preview_result_df = None  # 保存预览时生成的结果
        self.preview_code = None  # 保存预览时的代码
        self.preview_has_plot = False  # 标记预览时是否包含图表
        
        # 加载配置
        self.config = load_config()
        
        # 初始化Ollama客户端，使用全局变量
        self.ollama = OllamaClient()

        # Streamlit相关变量
        self.streamlit_process = None
        self.streamlit_port = 8501
        self.temp_data_file = None

        self.create_widgets()

        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        

    def create_widgets(self):
        # 按钮区域
        button_frame = ttk.Frame(self.root)
        button_frame.pack(padx=10, pady=5, fill="x")
        
        # 操作按钮 - 按新的顺序排列
        load_btn = ttk.Button(button_frame, text="加载数据", command=self.load_data)
        load_btn.pack(side="left", padx=5, pady=5)
        
        # AI对话按钮
        ai_chat_btn = ttk.Button(button_frame, text="AI对话", command=self.open_ai_chat)
        ai_chat_btn.pack(side="left", padx=5, pady=5)

        # 数据预览按钮（Streamlit）
        data_preview_btn = ttk.Button(button_frame, text="数据预览",
                                     command=lambda: self._launch_streamlit_preview(self.current_df, "数据预览", "data"))
        data_preview_btn.pack(side="left", padx=5, pady=5)
        
        # AI分类标注
        ai_categorize_btn = ttk.Button(button_frame, text="AI分类标注",
                            command=lambda: self.run_special_op("ai_categorize"))
        ai_categorize_btn.pack(side="left", padx=5, pady=5)
        
        # 透视表
        pivot_btn = ttk.Button(button_frame, text="透视表", 
                            command=lambda: self.run_special_op("pivot_table"))
        pivot_btn.pack(side="left", padx=5, pady=5)
        
        # 多条件筛选
        filter_btn = ttk.Button(button_frame, text="多条件筛选", 
                            command=lambda: self.run_special_op("multi_filter"))
        filter_btn.pack(side="left", padx=5, pady=5)
        
        # 删除空白行
        drop_blank_btn = ttk.Button(button_frame, text="删除空白行",
                            command=lambda: self.run_special_op("drop_blank_rows"))
        drop_blank_btn.pack(side="left", padx=5, pady=5)
        
        # 创建新按钮框架用于放置更多按钮
        button_frame2 = ttk.Frame(self.root)
        button_frame2.pack(padx=10, pady=2, fill="x")
        
        # 删除行
        delete_rows_btn = ttk.Button(button_frame2, text="删除行", 
                            command=lambda: self.run_special_op("delete_rows"))
        delete_rows_btn.pack(side="left", padx=5, pady=5)
        
        # 删除重复行
        delete_duplicates_btn = ttk.Button(button_frame2, text="删除重复行", 
                            command=lambda: self.run_special_op("drop_duplicates"))
        delete_duplicates_btn.pack(side="left", padx=5, pady=5)
        
        # 两列相似度分析
        similarity_btn = ttk.Button(button_frame2, text="相似度分析", 
                            command=lambda: self.run_special_op("similarity_analysis"))
        similarity_btn.pack(side="left", padx=5, pady=5)
        
        # 创建列
        create_col_btn = ttk.Button(button_frame2, text="创建列", 
                            command=lambda: self.run_special_op("create_column"))
        create_col_btn.pack(side="left", padx=5, pady=5)
        
        # 替换值
        replace_vals_btn = ttk.Button(button_frame2, text="替换值", 
                            command=lambda: self.run_special_op("replace_values"))
        replace_vals_btn.pack(side="left", padx=5, pady=5)
        
        # 转换类型
        convert_type_btn = ttk.Button(button_frame2, text="转换类型", 
                            command=lambda: self.run_special_op("convert_type"))
        convert_type_btn.pack(side="left", padx=5, pady=5)
        
        # 处理缺失值
        handle_missing_btn = ttk.Button(button_frame2, text="处理缺失值", 
                            command=lambda: self.run_special_op("handle_missing"))
        handle_missing_btn.pack(side="left", padx=5, pady=5)
        
        # 标准化
        normalize_btn = ttk.Button(button_frame2, text="标准化", 
                            command=lambda: self.run_special_op("normalize_data"))
        normalize_btn.pack(side="left", padx=5, pady=5)
        
        # 右侧按钮
        clear_btn = ttk.Button(button_frame, text="清空记录", command=self.clear_history)
        clear_btn.pack(side="right", padx=5, pady=5)
        save_btn = ttk.Button(button_frame, text="保存历史", command=self.save_history)
        save_btn.pack(side="right", padx=5, pady=5)
        
        # 日志区域
        chat_frame = ttk.LabelFrame(self.root, text="日志/结果")
        chat_frame.pack(padx=10, pady=5, expand=True, fill="both")
        
        # 使用Frame+Scrollbar+Text组合优化滚动体验
        chat_scroll_frame = ttk.Frame(chat_frame)
        chat_scroll_frame.pack(padx=5, pady=5, expand=True, fill="both")
        
        # 添加垂直滚动条
        chat_scrollbar = ttk.Scrollbar(chat_scroll_frame, orient="vertical")
        chat_scrollbar.pack(side="right", fill="y")
        
        # 添加水平滚动条
        chat_h_scrollbar = ttk.Scrollbar(chat_scroll_frame, orient="horizontal")
        chat_h_scrollbar.pack(side="bottom", fill="x")
        
        # 创建文本显示区域并关联滚动条
        self.chat_display = scrolledtext.ScrolledText(
            chat_scroll_frame,
            wrap=tk.NONE,  # 设置为NONE允许水平滚动
            state="disabled",
            yscrollcommand=chat_scrollbar.set,
            xscrollcommand=chat_h_scrollbar.set,
            font=("Courier New", 10)  # 使用等宽字体确保表格对齐
        )
        self.chat_display.pack(expand=True, fill="both")

        # 配置滚动条与文本区域的关联
        chat_scrollbar.config(command=self.chat_display.yview)
        chat_h_scrollbar.config(command=self.chat_display.xview)

        # 配置文本样式标签，用于美化显示 - streamlit风格
        self.chat_display.tag_configure("separator", foreground="#666666")
        self.chat_display.tag_configure("timestamp", foreground="#0066CC", font=("Arial", 9))
        self.chat_display.tag_configure("streamlit_table", font=("Courier New", 9), foreground="#000000")
        self.chat_display.tag_configure("header_border", foreground="#FF6B35", font=("Courier New", 10, "bold"))  # streamlit橙色
        self.chat_display.tag_configure("header_text", foreground="#FF6B35", font=("Microsoft YaHei", 10, "bold"))
        self.chat_display.tag_configure("data_content", font=("Courier New", 9), foreground="#333333")
        self.chat_display.tag_configure("column_border", foreground="#FF6B35")  # streamlit主色调
        self.chat_display.tag_configure("column_header", foreground="#FF6B35", font=("Microsoft YaHei", 9, "bold"))
        self.chat_display.tag_configure("column_content", font=("Courier New", 9), foreground="#555555")
        self.chat_display.tag_configure("system_icon", foreground="#2196F3")
        self.chat_display.tag_configure("system_message", foreground="#1976D2")
        self.chat_display.tag_configure("error_icon", foreground="#F44336")
        self.chat_display.tag_configure("error_message", foreground="#D32F2F")
        self.chat_display.tag_configure("success_icon", foreground="#4CAF50")
        self.chat_display.tag_configure("success_message", foreground="#388E3C")
        self.chat_display.tag_configure("load_icon", foreground="#FF6B35")  # streamlit橙色
        self.chat_display.tag_configure("load_message", foreground="#FF6B35")
        
        # 配置标签样式
        self.chat_display.tag_configure("data_preview", font=("Courier New", 10), foreground="darkblue", background="#f0f0f0")
        
        # 可视化区域
        self.viz_frame = ttk.LabelFrame(self.root, text="数据可视化")
        
        # 结果操作区域
        self.result_frame = ttk.Frame(self.root)

        # 在create_widgets方法中，主界面按钮区域下方增加模型选择框
        # 模型选择区域（主界面）
        model_frame = ttk.Frame(self.root)
        model_frame.pack(padx=10, pady=2, fill="x")
        ttk.Label(model_frame, text="使用模型:").pack(side="left", padx=5)
        self.model_var = tk.StringVar(value=OLLAMA_MODEL)
        self.model_cb = ttk.Combobox(model_frame, textvariable=self.model_var, width=20)
        self.model_cb.pack(side="left", padx=5)
        refresh_btn = ttk.Button(model_frame, text="刷新模型列表", command=lambda: self._refresh_models(self.model_cb))
        refresh_btn.pack(side="left", padx=5)
        # 初始化模型列表
        self.model_cb['values'] = [OLLAMA_MODEL]
        # 绑定模型选择事件
        def on_model_change(*args):
            model_name = self.model_var.get()
            self.ollama.set_model(model_name)
            global OLLAMA_MODEL
            OLLAMA_MODEL = model_name
            self.config["ollama_model"] = model_name
            save_config(self.config)
        self.model_var.trace_add('write', on_model_change)

    def load_data(self):
        """加载Excel或CSV文件"""
        # 使用上次打开的目录作为初始目录
        initial_dir = self.config.get("last_dir", "")
        if not os.path.exists(initial_dir):
            initial_dir = os.getcwd()

        file_path = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("CSV文件", "*.csv")],
            initialdir=initial_dir
        )
        if not file_path:
            return

        # 保存当前目录到配置
        self.config["last_dir"] = os.path.dirname(file_path)
        save_config(self.config)

        self.update_chat(f"已选择文件: {file_path}")
        self.current_path = file_path

        try:
            # 根据文件类型加载数据
            if file_path.lower().endswith(('.xlsx', '.xls')):
                # 检查是否有多个工作表
                excel_file = pd.ExcelFile(file_path)
                sheet_names = excel_file.sheet_names

                if len(sheet_names) > 1:
                    # 有多个工作表，让用户选择
                    selected_sheet = self._select_worksheet(sheet_names)
                    if selected_sheet is None:
                        return  # 用户取消选择
                    self.current_df = pd.read_excel(file_path, sheet_name=selected_sheet)
                    self.current_sheet = selected_sheet
                    self.update_chat(f"已选择工作表: {selected_sheet}")
                else:
                    # 只有一个工作表
                    self.current_df = pd.read_excel(file_path)
                    self.current_sheet = sheet_names[0] if sheet_names else 'Sheet1'

            elif file_path.lower().endswith('.csv'):
                self.current_df = pd.read_csv(file_path, encoding='utf-8-sig')
                self.current_sheet = None  # CSV文件没有工作表概念
            else:
                self.update_chat("不支持的文件格式")
                return

            # 保存原始数据副本和初始化状态
            self._original_df = self.current_df.copy()
            self._file_modified = False

            # 显示基本信息
            sheet_info = f" (工作表: {self.current_sheet})" if self.current_sheet else ""
            self.update_chat(f"文件已加载{sheet_info}，共{self.current_df.shape[0]}行，{self.current_df.shape[1]}列")
            self.update_chat(f"列名: {', '.join(str(col) for col in self.current_df.columns)}")

            # 使用新的格式化方法显示数据预览
            preview = self._format_dataframe_preview(self.current_df, max_rows=8)
            self.update_chat(f"数据预览:\n{preview}")
        except Exception as e:
            self.update_chat(f"加载文件失败: {str(e)}")
            traceback.print_exc()

    def _select_worksheet(self, sheet_names):
        """显示工作表选择对话框"""
        # 创建选择对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("选择工作表")
        dialog.geometry("400x300")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        selected_sheet = None

        # 标题
        ttk.Label(dialog, text="检测到多个工作表，请选择要加载的工作表:",
                 font=("Arial", 10)).pack(pady=10)

        # 工作表列表
        list_frame = ttk.Frame(dialog)
        list_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # 创建列表框和滚动条
        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side="right", fill="y")

        listbox = tk.Listbox(list_frame, yscrollcommand=scrollbar.set,
                            font=("Arial", 10), height=8)
        listbox.pack(side="left", fill="both", expand=True)
        scrollbar.config(command=listbox.yview)

        # 添加工作表名称
        for i, sheet_name in enumerate(sheet_names):
            listbox.insert(tk.END, f"{i+1}. {sheet_name}")

        # 默认选择第一个
        listbox.selection_set(0)

        # 按钮区域
        btn_frame = ttk.Frame(dialog)
        btn_frame.pack(fill="x", padx=20, pady=10)

        def on_confirm():
            nonlocal selected_sheet
            selection = listbox.curselection()
            if selection:
                selected_sheet = sheet_names[selection[0]]
                dialog.destroy()

        def on_cancel():
            dialog.destroy()

        # 双击列表项也可以确认
        listbox.bind("<Double-Button-1>", lambda e: on_confirm())

        ttk.Button(btn_frame, text="确定", command=on_confirm).pack(side="right", padx=5)
        ttk.Button(btn_frame, text="取消", command=on_cancel).pack(side="right", padx=5)

        # 等待对话框关闭
        dialog.wait_window()

        return selected_sheet

    def _show_save_options_dialog(self, title="保存选项"):
        """显示保存选项对话框，返回用户选择：'overwrite', 'save_as', 或 None(取消)"""
        # 创建选择对话框
        dialog = tk.Toplevel(self.root)
        dialog.title(title)
        dialog.geometry("420x280")  # 增大对话框尺寸
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        result = None

        # 主框架
        main_frame = ttk.Frame(dialog)
        main_frame.pack(fill="both", expand=True, padx=25, pady=25)

        # 标题
        title_label = ttk.Label(main_frame, text="请选择保存方式:",
                               font=("Microsoft YaHei", 12, "bold"))
        title_label.pack(pady=(0, 20))

        # 选项框架
        options_frame = ttk.Frame(main_frame)
        options_frame.pack(fill="x", pady=15)

        # 覆盖原文件选项
        overwrite_frame = ttk.Frame(options_frame)
        overwrite_frame.pack(fill="x", pady=8)

        overwrite_btn = ttk.Button(overwrite_frame, text="📝 覆盖原文件",
                                  command=lambda: self._set_save_result(dialog, 'overwrite'),
                                  width=18)
        overwrite_btn.pack(side="left")
        ttk.Label(overwrite_frame, text="直接修改当前文件（原文件将被替换）",
                 foreground="#666666", font=("Microsoft YaHei", 9)).pack(side="left", padx=(15, 0))

        # 另存为选项
        save_as_frame = ttk.Frame(options_frame)
        save_as_frame.pack(fill="x", pady=8)

        save_as_btn = ttk.Button(save_as_frame, text="💾 另存为",
                                command=lambda: self._set_save_result(dialog, 'save_as'),
                                width=18)
        save_as_btn.pack(side="left")
        ttk.Label(save_as_frame, text="保存到新文件（保留原文件）",
                 foreground="#666666", font=("Microsoft YaHei", 9)).pack(side="left", padx=(15, 0))

        # 分隔线
        separator = ttk.Separator(main_frame, orient='horizontal')
        separator.pack(fill="x", pady=(25, 15))

        # 按钮区域
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill="x")

        # 取消按钮 - 增强显示效果
        cancel_btn = ttk.Button(btn_frame, text="❌ 取消",
                               command=lambda: self._set_save_result(dialog, None),
                               width=12)
        cancel_btn.pack(side="right", padx=(10, 0))

        # 添加提示信息
        info_label = ttk.Label(btn_frame, text="💡 提示：取消将仅在内存中保留更改",
                              foreground="#888888", font=("Microsoft YaHei", 8))
        info_label.pack(side="left")

        # 保存结果的变量
        dialog.save_result = None

        # 设置默认焦点到覆盖按钮
        overwrite_btn.focus_set()

        # 键盘快捷键
        dialog.bind('<Return>', lambda e: self._set_save_result(dialog, 'overwrite'))
        dialog.bind('<Escape>', lambda e: self._set_save_result(dialog, None))

        # 等待对话框关闭
        dialog.wait_window()

        return getattr(dialog, 'save_result', None)

    def _set_save_result(self, dialog, result):
        """设置保存结果并关闭对话框"""
        dialog.save_result = result
        dialog.destroy()

    def save_history(self):
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt")],
            initialfile="操作历史.txt"
        )
        if not file_path:
            return
        try:
            self.chat_display.config(state="normal")
            text = self.chat_display.get("1.0", tk.END)
            self.chat_display.config(state="disabled")
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(text)
            self.update_chat(f"已保存历史到: {file_path}")
        except Exception as e:
            self.update_chat(f"保存文件失败: {str(e)}")
            traceback.print_exc()

    def clear_history(self):
        """清空主界面历史记录"""
        self.chat_display.config(state="normal")
        self.chat_display.delete("1.0", tk.END)
        self.chat_display.config(state="disabled")
        
        # 恢复到原始数据
        if self._original_df is not None and self.current_df is not None:
            self.current_df = self._original_df.copy()
            self._file_modified = False
            
        self.update_chat("记录已清空")
        
        # 如果有数据，显示基本信息
        if self.current_df is not None:
            self.update_chat(f"已恢复原始数据，共{self.current_df.shape[0]}行，{self.current_df.shape[1]}列")

    def open_ai_chat(self):
        """打开AI对话窗口"""
        global OLLAMA_MODEL
        # 检查是否已加载数据
        if self.current_df is None:
            self.update_chat("请先加载数据文件，然后再使用AI对话功能")
            return
        
        # 创建对话窗口
        chat_win = tk.Toplevel(self.root)
        chat_win.title("AI自然语言处理Excel数据")
        chat_win.geometry("900x700")  # 增加窗口大小
        chat_win.minsize(800, 600)  # 增加最小尺寸
        chat_win.resizable(True, True)
        
        # 清除之前的预览结果
        self.preview_result_df = None
        self.preview_code = None
        
        # 创建主布局框架
        main_frame = ttk.Frame(chat_win)
        main_frame.pack(fill="both", expand=True)
        
        # 创建左侧框架用于放置按钮
        left_frame = ttk.Frame(main_frame, width=100)
        left_frame.pack(side="left", fill="y", padx=10, pady=10)
        
        # 创建右侧框架用于放置内容区域
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side="left", fill="both", expand=True, padx=5, pady=10)
        
        # 创建按钮区域 - 竖向排列
        btn_frame = ttk.Frame(left_frame)
        btn_frame.pack(anchor="n", pady=10)
        
        preview_btn = ttk.Button(btn_frame, text="预览", width=10)
        preview_btn.pack(pady=5)

        execute_btn = ttk.Button(btn_frame, text="保存预览", width=10)
        execute_btn.pack(pady=5)
        
        # 修改关闭按钮的行为，清除预览结果
        close_btn = ttk.Button(btn_frame, text="关闭", width=10, 
                              command=lambda: self._close_ai_dialog(chat_win))
        close_btn.pack(pady=5)
        
        # 创建主滚动区域
        main_canvas = tk.Canvas(right_frame)
        scrollbar = ttk.Scrollbar(right_frame, orient="vertical", command=main_canvas.yview)
        scrollable_frame = ttk.Frame(main_canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: main_canvas.configure(scrollregion=main_canvas.bbox("all"))
        )
        
        main_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        main_canvas.configure(yscrollcommand=scrollbar.set)
        
        # 对话历史显示区域
        history_label = ttk.Label(scrollable_frame, text="对话历史")
        history_label.pack(anchor="w", padx=5, pady=2)
        
        history_text = scrolledtext.ScrolledText(scrollable_frame, wrap=tk.WORD, height=15)
        history_text.pack(fill="both", expand=True, padx=5, pady=5)
        history_text.tag_configure("user", foreground="blue")
        history_text.tag_configure("ai", foreground="green")
        history_text.tag_configure("system", foreground="gray")
        history_text.tag_configure("error", foreground="red")
        
        # 参数区域
        param_frame = ttk.LabelFrame(scrollable_frame, text="对话参数")
        param_frame.pack(fill="x", padx=5, pady=5)
        
        # 系统提示区域
        system_frame = ttk.Frame(param_frame)
        system_frame.pack(fill="x", padx=5, pady=5)
        
        ttk.Label(system_frame, text="系统提示:").pack(anchor="w", padx=5)
        system_text = scrolledtext.ScrolledText(system_frame, wrap=tk.WORD, height=3)
        system_text.pack(fill="both", padx=5, pady=5)
        system_text.insert("1.0", self.config.get("system_prompt", DEFAULT_CONFIG["system_prompt"]))
        
        # 添加保存系统提示的按钮
        save_prompt_btn = ttk.Button(system_frame, text="保存为默认",
                                    command=lambda: self._save_system_prompt(system_text))
        save_prompt_btn.pack(anchor="e", padx=5, pady=2)
        
        # 输入区域
        input_frame = ttk.Frame(param_frame)
        input_frame.pack(fill="x", padx=5, pady=5)
        
        ttk.Label(input_frame, text="输入请求:").pack(anchor="w", padx=5)
        input_text = scrolledtext.ScrolledText(input_frame, wrap=tk.WORD, height=4)
        input_text.pack(fill="x", padx=5, pady=5)
        

        
        # 将主画布和滚动条放入窗口
        main_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 预览区
        preview_frame = ttk.LabelFrame(scrollable_frame, text="预览区")
        preview_frame.pack(fill="both", expand=True, padx=5, pady=5)
        preview_text = scrolledtext.ScrolledText(preview_frame, height=10, font=("Courier New", 10))  # 使用等宽字体
        preview_text.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 设置按钮功能
        preview_btn.configure(command=lambda: self._preview_ai_operation_with_streamlit(input_text, history_text, system_text, preview_text))
        execute_btn.configure(command=lambda: self._execute_ai_operation(input_text, history_text, system_text))
        
        # 初始显示系统消息
        history_text.insert("1.0", "系统: AI对话功能已启动，请描述您要对数据执行的操作\n", "system")
        
        # 移除Ollama服务可用性检查

        # 加载可用模型
        # model_cb['values'] = [OLLAMA_MODEL]  # 默认只显示当前模型，使用全局变量
        
        # 显示当前数据集信息
        df_info = (f"当前数据集: {self.current_path}\n"
                  f"行数: {self.current_df.shape[0]}, 列数: {self.current_df.shape[1]}\n"
                  f"列名: {', '.join(str(col) for col in self.current_df.columns)}")
        history_text.insert("end", f"系统: {df_info}\n", "system")
        history_text.insert("end", "系统: 请输入您想对数据做什么操作，例如: \n- '删除所有缺失值的行' \n- '按年龄分组计算平均值' \n- '找出销售额最高的前5个产品' \n- '创建一个年龄与收入的散点图'\n", "system")

    def _preview_ai_operation(self, input_text, history_text, system_text, preview_text):
        """预览AI操作结果"""
        global OLLAMA_MODEL
        user_input = input_text.get("1.0", "end-1c").strip()
        if not user_input:
            return
            
        # 如果没有预览文本框，则找到它
        if preview_text is None:
            # 尝试在窗口中找到预览文本框
            chat_win = history_text.winfo_toplevel()
            for widget in chat_win.winfo_children():
                if isinstance(widget, ttk.Frame):  # 主框架
                    for child in widget.winfo_children():
                        if isinstance(child, tk.Canvas):  # 主Canvas
                            for canvas_child in child.winfo_children():
                                if isinstance(canvas_child, ttk.Frame):  # scrollable_frame
                                    for frame_child in canvas_child.winfo_children():
                                        if isinstance(frame_child, ttk.LabelFrame) and frame_child["text"] == "预览区":
                                            for preview_widget in frame_child.winfo_children():
                                                if isinstance(preview_widget, scrolledtext.ScrolledText):
                                                    preview_text = preview_widget
                                                    break
            
            # 如果仍然找不到预览文本框，直接返回
            if preview_text is None:
                history_text.insert("end", "系统: 无法找到预览区域\n", "system")
                return
        
        preview_text.delete("1.0", tk.END)
        
        # 清除之前的预览结果
        self.preview_result_df = None
        self.preview_code = None
        
        # 确保数据已加载
        if self.current_df is None:
            preview_text.insert("end", "错误: 没有加载数据\n", "error")
            return
            
        # 更新模型
        model_name = self.model_var.get()
        if not model_name:
            model_name = OLLAMA_MODEL  # 使用全局变量
            self.model_var.set(model_name)
        self.ollama.set_model(model_name)
        
        # 显示用户输入
        history_text.insert("end", f"用户: {user_input}\n", "user")
        
        # 构建提示
        system_prompt = system_text.get("1.0", "end-1c")
        
        # 添加数据集信息
        df_info = (f"当前数据集: {self.current_path}\n"
                  f"- 行数: {self.current_df.shape[0]}, 列数: {self.current_df.shape[1]}\n"
                  f"- 列名: {', '.join(str(col) for col in self.current_df.columns)}\n"
                  f"- 数据类型: {', '.join(f'{col}: {self.current_df[col].dtype}' for col in self.current_df.columns[:5])}"
                  f"{' 等...' if len(self.current_df.columns) > 5 else ''}\n"
                  f"- 前5行预览:\n{self.current_df.head(5).to_string()}")
        
        prompt = f"{df_info}\n\n用户请求: {user_input}\n\n请给出处理这个请求的pandas代码和简短解释。代码应该可以直接运行，并用```python和```包裹。请在代码中使用'self.current_df'作为数据变量名，并将最终结果赋值给变量'result'。请处理好可能的错误情况，如索引不存在等。"
        
        # 发送到AI
        preview_text.insert("end", f"正在使用模型 {model_name} 思考中...\n")
        preview_text.update()
        
        try:
            response = self.ollama.ask(prompt, system_prompt)
            
            # 显示AI回复
            history_text.insert("end", f"AI: {response}\n", "ai")
            history_text.see("end")
            
            # 显示在预览区域
            preview_text.insert("end", "AI回复:\n")
            preview_text.insert("end", response)
            
            # 尝试提取代码
            code_blocks = re.findall(r'```(?:python)?(.*?)```', response, re.DOTALL)
            if code_blocks:
                code = code_blocks[0].strip()
                # 预处理代码以增强兼容性
                code = self._preprocess_code(code)
                # 保存预览代码供执行时使用
                self.preview_code = code
                
                preview_text.insert("end", "\n\n预处理后的代码:\n")
                preview_text.insert("end", code)
                
                # 使用当前数据的副本安全执行代码，预览结果但不修改原始数据
                preview_text.insert("end", "\n\n预期执行结果:\n")
                preview_text.update()
                
                # 创建安全的局部变量环境，使用当前数据的副本
                locals_dict = {
                    'pd': pd, 
                    'np': np,
                    're': re,
                    'plt': plt
                }
                
                # 创建一个临时类以模拟self
                class TempSelf:
                    def __init__(self, df):
                        self.current_df = df.copy()
                        
                temp_self = TempSelf(self.current_df)
                locals_dict['self'] = temp_self
                
                # 捕获标准输出
                old_stdout = sys.stdout
                redirected_output = io.StringIO()
                sys.stdout = redirected_output
                
                try:
                    # 执行代码
                    exec(code, None, locals_dict)
                    
                    # 检查代码中是否包含绘图操作
                    has_plot = 'plt.show()' in code or 'plt.plot' in code or '.plot(' in code or 'plt.figure' in code
                    
                    # 获取打印输出
                    printed_output = redirected_output.getvalue()
                    if printed_output:
                        preview_text.insert("end", "代码输出:\n" + printed_output + "\n")
                    
                    # 检查代码是否生成了结果
                    if 'result' in locals_dict:
                        result_df = locals_dict['result']
                        if isinstance(result_df, pd.DataFrame):
                            # 保存预览结果供执行时使用
                            self.preview_result_df = result_df.copy()
                            
                            # 使用streamlit风格显示预览
                            streamlit_preview = self._create_streamlit_preview(
                                result_df,
                                "AI操作结果",
                                max_rows=10,
                                max_cols=8
                            )
                            preview_text.insert("end", streamlit_preview + "\n")
                            
                            # 显示数据形状变化
                            orig_shape = self.current_df.shape
                            new_shape = result_df.shape
                            shape_info = f"数据变化: {orig_shape[0]}行×{orig_shape[1]}列 -> {new_shape[0]}行×{new_shape[1]}列\n"
                            preview_text.insert("end", shape_info)
                            
                            # 如果列发生变化，显示列变化
                            orig_cols = set(self.current_df.columns)
                            new_cols = set(result_df.columns)
                            added_cols = new_cols - orig_cols
                            removed_cols = orig_cols - new_cols
                            
                            if added_cols:
                                preview_text.insert("end", f"新增列: {', '.join(str(col) for col in added_cols)}\n")
                            if removed_cols:
                                preview_text.insert("end", f"移除列: {', '.join(str(col) for col in removed_cols)}\n")
                        elif isinstance(result_df, pd.Series):
                            # 保存Series类型的结果
                            self.preview_result_df = result_df.copy()

                            # 将Series转换为DataFrame以便使用streamlit风格显示
                            series_df = result_df.to_frame(name='结果')
                            streamlit_preview = self._create_streamlit_preview(
                                series_df,
                                "AI操作结果 (Series)",
                                max_rows=10,
                                max_cols=1
                            )
                            preview_text.insert("end", streamlit_preview + "\n")
                        else:
                            # 保存其他类型的结果
                            self.preview_result_df = result_df
                            preview_text.insert("end", f"结果: {str(result_df)}\n")
                    else:
                        # 如果没有explicit结果但修改了dataframe
                        if hasattr(temp_self, 'current_df') and not temp_self.current_df.equals(self.current_df):
                            # 保存修改后的DataFrame
                            self.preview_result_df = temp_self.current_df.copy()
                            
                            # 使用streamlit风格显示预览
                            streamlit_preview = self._create_streamlit_preview(
                                temp_self.current_df,
                                "修改后的数据预览",
                                max_rows=8,
                                max_cols=6
                            )
                            preview_text.insert("end", streamlit_preview + "\n")
                            
                            # 显示数据形状变化
                            orig_shape = self.current_df.shape
                            new_shape = temp_self.current_df.shape
                            shape_info = f"数据变化: {orig_shape[0]}行×{orig_shape[1]}列 -> {new_shape[0]}行×{new_shape[1]}列\n"
                            preview_text.insert("end", shape_info)
                    
                    # 如果检测到绘图操作，添加图表预览信息
                    if has_plot and plt.get_fignums():
                        preview_text.insert("end", "\n检测到绘图操作，正在准备图表预览...\n")
                        # 保存图表但不在预览阶段显示
                        self.preview_has_plot = True
                        
                        # 获取当前图表
                        fig = plt.gcf()
                        
                        # 修复数据标签问题 - 检查所有子图并调整数据标签位置
                        for ax in fig.get_axes():
                            # 检查是否有文本对象（数据标签）
                            for text in ax.texts:
                                # 获取文本位置
                                x, y = text.get_position()
                                # 如果是条形图，将标签移到条形中间
                                if hasattr(ax, 'patches') and ax.patches:
                                    # 对于条形图，将标签放在条形中间
                                    text.set_va('center')
                        
                        # 创建一个临时的顶层窗口来显示图表预览
                        preview_fig_win = tk.Toplevel(preview_text.winfo_toplevel())
                        preview_fig_win.title("图表预览")
                        preview_fig_win.geometry("600x400")
                        
                        # 创建canvas显示图表
                        preview_canvas = FigureCanvasTkAgg(fig, master=preview_fig_win)
                        preview_canvas.draw()
                        preview_canvas.get_tk_widget().pack(fill="both", expand=True)
                        
                        # 添加关闭按钮
                        close_btn = ttk.Button(preview_fig_win, text="关闭预览", 
                                             command=preview_fig_win.destroy)
                        close_btn.pack(pady=10)
                        
                        preview_text.insert("end", "图表已在新窗口中预览，执行后将在主界面显示\n")
                        
                        # 关闭matplotlib的图表以避免内存泄漏，但保留我们的预览
                        plt.close('all')
                
                except Exception as e:
                    preview_text.insert("end", f"预览执行错误: {str(e)}\n")
                finally:
                    # 恢复标准输出
                    sys.stdout = old_stdout
            else:
                preview_text.insert("end", "\n\n无法从回复中提取Python代码，请检查回复内容\n")
            
            # 确保滚动到底部
            preview_text.see("end")



        except Exception as e:
            preview_text.insert("end", f"与AI模型通信失败: {str(e)}\n")
            traceback.print_exc()

    def _execute_ai_operation(self, input_text, history_text, system_text):
        """执行AI操作并更新当前DataFrame"""
        global OLLAMA_MODEL
        user_input = input_text.get("1.0", "end-1c").strip()
        if not user_input:
            return
            
        # 确保数据已加载
        if self.current_df is None:
            history_text.insert("end", "系统: 错误 - 没有加载数据\n", "system")
            return
            
        # 更新模型
        model_name = self.model_var.get()
        if not model_name:
            model_name = OLLAMA_MODEL  # 使用全局变量
            self.model_var.set(model_name)
        self.ollama.set_model(model_name)
        
        # 更新配置
        if model_name != self.config.get("ollama_model"):
            self.config["ollama_model"] = model_name
            OLLAMA_MODEL = model_name  # 更新全局变量
            save_config(self.config)
        
        # 检查是否有预览结果
        if self.preview_result_df is not None and user_input:
            # 有预览结果，直接使用
            history_text.insert("end", f"用户: {user_input}\n", "user")
            input_text.delete("1.0", "end")
            
            # 如果有图表需要显示
            if self.preview_has_plot and self.preview_code:
                history_text.insert("end", "系统: 正在生成图表...\n", "system")
                
                # 执行代码以生成图表
                try:
                    # 执行代码重新生成图表
                    locals_dict = {
                        'pd': pd, 
                        'np': np,
                        'self': self,
                        'plt': plt,
                        're': re
                    }
                    
                    exec(self.preview_code, None, locals_dict)
                    
                    # 检查是否生成了图表
                    if plt.get_fignums():
                        # 获取当前图表
                        fig = plt.gcf()
                        self.current_figure = fig
                        
                        # 如果不存在可视化区域，创建它
                        if not self.viz_frame or not self.viz_frame.winfo_exists():
                            self.viz_frame = ttk.LabelFrame(self.root, text="数据可视化")
                            self.viz_frame.pack(padx=10, pady=5, fill="both", expand=True)
                        else:
                            # 清除当前内容
                            for widget in self.viz_frame.winfo_children():
                                widget.destroy()
                        
                        # 创建canvas显示图表
                        self.canvas = FigureCanvasTkAgg(fig, master=self.viz_frame)
                        self.canvas.draw()
                        self.canvas.get_tk_widget().pack(fill="both", expand=True)
                        
                        # 添加图表操作区域
                        btn_frame = ttk.Frame(self.viz_frame)
                        btn_frame.pack(fill="x", padx=5, pady=5)
                        
                        # 添加保存按钮
                        save_btn = ttk.Button(btn_frame, text="保存图表", 
                                            command=lambda: self.save_current_figure())
                        save_btn.pack(side="left", padx=5)
                        
                        # 添加复制到剪贴板按钮（如果支持）
                        if sys.platform == 'win32' and 'win32clipboard' in sys.modules:
                            copy_btn = ttk.Button(btn_frame, text="复制到剪贴板", 
                                                command=lambda: self.copy_figure_to_clipboard())
                            copy_btn.pack(side="left", padx=5)
                            
                        history_text.insert("end", "系统: 图表已生成\n", "system")
                except Exception as e:
                    history_text.insert("end", f"系统: 图表生成失败: {str(e)}\n", "error")
                
                # 重置图表标记
                self.preview_has_plot = False
            
            # 如果是DataFrame或Series，直接更新当前数据
            if isinstance(self.preview_result_df, pd.DataFrame):
                self.current_df = self.preview_result_df.copy()
                self._file_modified = True
                result_info = f"已使用预览结果更新数据集，新大小: {self.current_df.shape[0]}行 x {self.current_df.shape[1]}列\n"

                # 使用streamlit风格显示执行结果
                streamlit_preview = self._create_streamlit_preview(self.current_df, "执行结果数据", max_rows=5, max_cols=6)
                result_info += f"数据预览:\n{streamlit_preview}\n"
                history_text.insert("end", f"系统: {result_info}\n", "system")
            elif isinstance(self.preview_result_df, pd.Series):
                # 对于Series类型结果，可以根据需要处理
                history_text.insert("end", f"系统: 已获取Series结果: {self.preview_result_df.name if hasattr(self.preview_result_df, 'name') else '未命名'}\n", "system")
                # 如果需要，可以将Series转换为DataFrame
                # self.current_df = pd.DataFrame(self.preview_result_df)
                # self._file_modified = True
            else:
                # 其他类型结果的处理
                history_text.insert("end", f"系统: 已获取结果: {str(self.preview_result_df)}\n", "system")
            
            # 如果数据已更新，询问保存方式
            if self._file_modified and self.current_path:
                # 使用streamlit风格显示更新后的数据预览
                streamlit_preview = self._create_streamlit_preview(self.current_df, "更新后的数据", max_rows=5, max_cols=6)
                history_text.insert("end", f"系统: {streamlit_preview}\n", "system")

                # 显示保存选项对话框
                save_option = self._show_save_options_dialog("保存数据")
                if save_option == 'overwrite':
                    # 覆盖原文件
                    save_result = self._save_to_original_file()
                    if save_result["success"]:
                        history_text.insert("end", f"系统: {save_result['message']}\n", "system")
                    else:
                        history_text.insert("end", f"系统: 保存失败: {save_result['message']}\n", "error")
                elif save_option == 'save_as':
                    # 另存为
                    save_result = self._save_as_new_file()
                    if save_result["success"]:
                        history_text.insert("end", f"系统: {save_result['message']}\n", "system")
                    else:
                        history_text.insert("end", f"系统: 保存失败: {save_result['message']}\n", "error")
                else:
                    # 用户取消保存
                    history_text.insert("end", "系统: 用户取消保存，数据仅在内存中更新\n", "system")
                
            history_text.see("end")
            return
            
        # 如果没有预览结果，则通过发送请求获取结果
        history_text.insert("end", f"用户: {user_input}\n", "user")
        input_text.delete("1.0", "end")
        
        # 构建提示
        system_prompt = system_text.get("1.0", "end-1c")
        
        # 添加数据集信息
        df_info = (f"当前数据集: {self.current_path}\n"
                  f"- 行数: {self.current_df.shape[0]}, 列数: {self.current_df.shape[1]}\n"
                  f"- 列名: {', '.join(str(col) for col in self.current_df.columns)}\n"
                  f"- 数据类型: {', '.join(f'{col}: {self.current_df[col].dtype}' for col in self.current_df.columns[:5])}"
                  f"{' 等...' if len(self.current_df.columns) > 5 else ''}\n"
                  f"- 前5行预览:\n{self.current_df.head(5).to_string()}")
        
        prompt = f"{df_info}\n\n用户请求: {user_input}\n\n请给出处理这个请求的pandas代码和简短解释。代码应该可以直接运行，并用```python和```包裹。请在代码中使用'self.current_df'作为数据变量名，并将最终结果赋值给变量'result'。请处理好可能的错误情况，如索引不存在等。"
        
        # 发送到AI - 添加等待提示和进度更新
        history_text.insert("end", f"系统: 正在使用模型 {model_name} 思考中...\n", "system")
        # 更新UI以显示正在等待
        chat_win = history_text.winfo_toplevel()
        chat_win.update()
        
        try:
            response = self.ollama.ask(prompt, system_prompt)
            
            # 显示AI回复
            history_text.insert("end", f"AI: {response}\n", "ai")
            history_text.see("end")
            
            # 尝试提取代码并执行
            code_blocks = re.findall(r'```(?:python)?(.*?)```', response, re.DOTALL)
            if code_blocks:
                code = code_blocks[0].strip()
                history_text.insert("end", "系统: 正在尝试执行代码并更新数据...\n", "system")
                
                # 预处理代码以增强兼容性
                code = self._preprocess_code(code)
                
                # 在执行前向用户展示代码
                history_text.insert("end", "系统: 实际保存的代码:\n```python\n", "system")
                history_text.insert("end", code, "code")
                history_text.insert("end", "\n```\n", "system")
                
                # 添加代码高亮
                history_text.tag_configure("code", foreground="dark blue", font=("Courier New", 10))
                
                # 安全执行代码
                result = self._safe_execute_code(code)
                history_text.insert("end", f"系统: 执行结果:\n{result}\n", "system")
                
                # 如果数据已更新，询问保存方式
                if self._file_modified and self.current_path:
                    # 显示更新后的数据预览
                    history_text.insert("end", f"系统: 更新后的数据预览 (前5行):\n{self.current_df.head(5).to_string()}\n", "system")

                    # 显示保存选项对话框
                    save_option = self._show_save_options_dialog("保存数据")
                    if save_option == 'overwrite':
                        # 覆盖原文件
                        save_result = self._save_to_original_file()
                        if save_result["success"]:
                            history_text.insert("end", f"系统: {save_result['message']}\n", "system")
                        else:
                            history_text.insert("end", f"系统: 保存失败: {save_result['message']}\n", "error")
                    elif save_option == 'save_as':
                        # 另存为
                        save_result = self._save_as_new_file()
                        if save_result["success"]:
                            history_text.insert("end", f"系统: {save_result['message']}\n", "system")
                        else:
                            history_text.insert("end", f"系统: 保存失败: {save_result['message']}\n", "error")
                    else:
                        # 用户取消保存
                        history_text.insert("end", "系统: 用户取消保存，数据仅在内存中更新\n", "system")
                    
                history_text.see("end")
            else:
                history_text.insert("end", "系统: 无法从回复中提取Python代码，请检查回复内容\n", "system")
        except Exception as e:
            history_text.insert("end", f"系统: 与AI模型通信失败: {str(e)}\n", "error")
            traceback.print_exc()

    def _save_to_original_file(self):
        """保存当前数据到原始文件并返回结果
        
        Excel文件的保存方式会确保：
        1. 只修改第一个工作表
        2. 其它工作表内容保持不变
        3. 使用openpyxl引擎处理复杂Excel文件
        """
        result = {"success": False, "message": ""}
        
        if self.current_df is None:
            result["message"] = "没有可保存的数据"
            return result
            
        if not self.current_path:
            result["message"] = "没有原始文件路径"
            return result
            
        try:
            if self.current_path.lower().endswith(('.xlsx', '.xls')):
                # 读取原文件的工作表名称
                try:
                    with pd.ExcelFile(self.current_path) as xls:
                        sheet_names = xls.sheet_names
                        first_sheet_name = str(sheet_names[0])  # 转换为字符串类型
                        
                        # 如果有多个工作表，先将它们读取出来
                        if len(sheet_names) > 1:
                            # 读取所有其他工作表内容
                            other_sheets = {}
                            for sheet_name in sheet_names[1:]:
                                sheet_name_str = str(sheet_name)
                                other_sheets[sheet_name_str] = pd.read_excel(self.current_path, sheet_name=sheet_name_str)
                                
                            # 创建ExcelWriter对象
                            with pd.ExcelWriter(self.current_path, engine='openpyxl', mode='w') as writer:
                                # 写入修改后的第一张表
                                self.current_df.to_excel(writer, sheet_name=first_sheet_name, index=False)
                                
                                # 保留其他工作表不变
                                for sheet_name, sheet_df in other_sheets.items():
                                    sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)
                                    
                            result["message"] = f"已更新第一张表({first_sheet_name})，其他工作表保持不变"
                        else:
                            # 只有一张表，直接保存
                            self.current_df.to_excel(self.current_path, sheet_name=first_sheet_name, index=False)
                            result["message"] = "已保存数据到工作表"
                except Exception as e:
                    # 如果读取原文件失败，则直接保存到新文件
                    sheet_name = 'Sheet1'  # 使用默认工作表名
                    self.current_df.to_excel(self.current_path, sheet_name=sheet_name, index=False)
                    result["message"] = f"保存到默认工作表: {sheet_name} (读取原工作表信息失败: {str(e)})"
            elif self.current_path.lower().endswith('.csv'):
                self.current_df.to_csv(self.current_path, index=False, encoding='utf-8-sig')
                result["message"] = "已保存数据到CSV文件"
            else:
                result["message"] = f"不支持的文件格式: {self.current_path}"
                return result
                
            # 自动重新加载数据
            try:
                if self.current_path.lower().endswith(('.xlsx', '.xls')):
                    # 只读取第一张表
                    reloaded_df = pd.read_excel(self.current_path, sheet_name=0)
                elif self.current_path.lower().endswith('.csv'):
                    reloaded_df = pd.read_csv(self.current_path, encoding='utf-8-sig')
                else:
                    raise ValueError("不支持的文件格式")
                
                # 更新当前数据和原始数据
                self.current_df = reloaded_df
                self._original_df = self.current_df.copy()
                self._file_modified = False
                
                result["success"] = True
                result["message"] += f"\n数据已自动重新加载: {self.current_path}"
            except Exception as e:
                result["message"] = f"文件已保存，但重新加载失败: {str(e)}"
                result["success"] = True  # 仍然视为保存成功
        except Exception as e:
            result["message"] = str(e)
            
        return result

    def _save_as_new_file(self):
        """另存为新文件"""
        result = {"success": False, "message": ""}

        try:
            # 确定文件扩展名
            if self.current_path:
                current_ext = os.path.splitext(self.current_path)[1].lower()
            else:
                current_ext = '.xlsx'  # 默认为Excel格式

            # 设置文件类型过滤器
            if current_ext == '.csv':
                filetypes = [("CSV文件", "*.csv"), ("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
                default_ext = '.csv'
            else:
                filetypes = [("Excel文件", "*.xlsx"), ("CSV文件", "*.csv"), ("所有文件", "*.*")]
                default_ext = '.xlsx'

            # 生成默认文件名
            if self.current_path:
                base_name = os.path.splitext(os.path.basename(self.current_path))[0]
                default_name = f"{base_name}_modified{default_ext}"
            else:
                default_name = f"data_modified{default_ext}"

            # 显示保存对话框
            new_file_path = filedialog.asksaveasfilename(
                title="另存为",
                defaultextension=default_ext,
                filetypes=filetypes,
                initialfilename=default_name,
                initialdir=os.path.dirname(self.current_path) if self.current_path else os.getcwd()
            )

            if not new_file_path:
                result["message"] = "用户取消保存"
                return result

            # 根据文件扩展名保存
            file_ext = os.path.splitext(new_file_path)[1].lower()

            if file_ext in ['.xlsx', '.xls']:
                # 保存为Excel文件
                if self.current_sheet:
                    # 如果有指定的工作表名，使用它
                    sheet_name = self.current_sheet
                else:
                    sheet_name = 'Sheet1'

                self.current_df.to_excel(new_file_path, sheet_name=sheet_name, index=False)
                result["message"] = f"已另存为Excel文件: {new_file_path} (工作表: {sheet_name})"

            elif file_ext == '.csv':
                # 保存为CSV文件
                self.current_df.to_csv(new_file_path, index=False, encoding='utf-8-sig')
                result["message"] = f"已另存为CSV文件: {new_file_path}"

            else:
                result["message"] = f"不支持的文件格式: {file_ext}"
                return result

            result["success"] = True

        except Exception as e:
            result["message"] = f"另存为失败: {str(e)}"
            traceback.print_exc()

        return result

    def _send_to_ai(self, input_text, history_text, system_text, execute_var):
        """原方法保留，但改为调用新方法"""
        if execute_var.get():
            self._execute_ai_operation(input_text, history_text, system_text)
        else:
            chat_win = history_text.winfo_toplevel()
            main_frame = None
            for child in chat_win.winfo_children():
                if isinstance(child, ttk.Frame):
                    main_frame = child
                    break
            if main_frame:
                for child in main_frame.winfo_children():
                    if isinstance(child, ttk.Frame):
                        for frame_child in child.winfo_children():
                            if isinstance(frame_child, ttk.Frame):
                                for btn in frame_child.winfo_children():
                                    if isinstance(btn, ttk.Button) and btn['text'] == "预览":
                                        btn.invoke()
                                        return
            # 如果找不到预览按钮，则执行原来的逻辑
            # 由于已删除自动执行功能，这里不再需要预览逻辑
            pass

    def _refresh_models(self, combobox, history_text=None):
        """刷新可用模型列表"""
        if not self.ollama.available:
            self.update_chat("无法连接到Ollama服务，请确保Ollama已在本地启动")
            if history_text:
                history_text.insert("end", "系统: 无法连接到Ollama服务，请确保Ollama已在本地启动\n", "error")
            return
        models = self.ollama.get_models()
        if models:
            combobox['values'] = models
            if history_text:
                history_text.insert("end", f"系统: 已加载可用模型: {', '.join(models[:5])}{' 等...' if len(models) > 5 else ''}\n", "system")
        else:
            self.update_chat("无法获取模型列表，请检查Ollama服务")
            if history_text:
                history_text.insert("end", "系统: 无法获取模型列表，请检查Ollama服务\n", "error")
    
    def _safe_execute_code(self, code):
        """安全地执行pandas代码，返回结果"""
        # 创建安全的局部变量环境
        locals_dict = {
            'pd': pd, 
            'np': np,
            'self': self,
            'plt': plt,
            're': re
        }
        
        # 捕获标准输出
        old_stdout = sys.stdout
        redirected_output = io.StringIO()
        sys.stdout = redirected_output
        
        result = ""
        try:
            # 执行代码
            exec(code, None, locals_dict)
            
            # 检查代码中是否包含绘图操作
            has_plot = 'plt.show()' in code or 'plt.plot' in code or '.plot(' in code or 'plt.figure' in code
            
            # 检查代码是否修改了current_df
            if 'result' in locals_dict:
                result_df = locals_dict['result']
                if isinstance(result_df, pd.DataFrame):
                    self.current_df = result_df
                    self._file_modified = True
                    result += f"已更新数据集，新大小: {self.current_df.shape[0]}行 x {self.current_df.shape[1]}列\n"
                    
                    # 使用streamlit风格显示预览
                    streamlit_preview = self._create_streamlit_preview(self.current_df, "执行结果", max_rows=5, max_cols=6)
                    result += f"数据预览:\n{streamlit_preview}\n"
                elif result_df is not None:
                    # 其他类型的结果直接打印
                    result += f"结果:\n{result_df}\n"
            
            # 如果检测到绘图操作，显示最后创建的图表
            if has_plot and plt.get_fignums():
                result += "检测到绘图操作，正在显示图表...\n"
                
                # 获取当前图表并显示
                fig = plt.gcf()  # 获取当前图表
                
                # 修复数据标签问题 - 检查所有子图并调整数据标签位置
                for ax in fig.get_axes():
                    # 检查是否有文本对象（数据标签）
                    for text in ax.texts:
                        # 获取文本位置
                        x, y = text.get_position()
                        # 如果是条形图，将标签移到条形内部
                        if hasattr(ax, 'patches') and ax.patches:
                            # 对于条形图，将标签放在条形中间
                            text.set_va('center')
                            
                # 存储为当前图表
                self.current_figure = fig
                
                # 如果不存在可视化区域，创建它
                if not self.viz_frame or not self.viz_frame.winfo_exists():
                    self.viz_frame = ttk.LabelFrame(self.root, text="数据可视化")
                    self.viz_frame.pack(padx=10, pady=5, fill="both", expand=True)
                else:
                    # 清除当前内容
                    for widget in self.viz_frame.winfo_children():
                        widget.destroy()
                
                # 创建canvas显示图表
                self.canvas = FigureCanvasTkAgg(fig, master=self.viz_frame)
                self.canvas.draw()
                self.canvas.get_tk_widget().pack(fill="both", expand=True)
                
                # 添加图表操作区域
                btn_frame = ttk.Frame(self.viz_frame)
                btn_frame.pack(fill="x", padx=5, pady=5)
                
                # 添加保存按钮
                save_btn = ttk.Button(btn_frame, text="保存图表", 
                                    command=lambda: self.save_current_figure())
                save_btn.pack(side="left", padx=5)
                
                # 添加复制到剪贴板按钮（如果支持）
                if sys.platform == 'win32' and 'win32clipboard' in sys.modules:
                    copy_btn = ttk.Button(btn_frame, text="复制到剪贴板", 
                                        command=lambda: self.copy_figure_to_clipboard())
                    copy_btn.pack(side="left", padx=5)
            
            # 获取打印输出
            printed_output = redirected_output.getvalue()
            if printed_output:
                result += "代码输出:\n" + printed_output
                
        except Exception as e:
            error_msg = str(e)
            result = f"执行错误: {error_msg}"
            traceback.print_exc()
            
            # 尝试自动修复常见错误
            fixed_code = self._try_fix_code(code, error_msg)
            if fixed_code and fixed_code != code:
                result += "\n\n正在尝试修复代码...\n"
                try:
                    # 重置标准输出
                    redirected_output = io.StringIO()
                    sys.stdout = redirected_output
                    
                    # 重新执行修复后的代码
                    exec(fixed_code, None, locals_dict)
                    
                    # 检查修复后的代码是否执行成功
                    result += "修复成功! 结果:\n"
                    if 'result' in locals_dict:
                        result_df = locals_dict['result']
                        if isinstance(result_df, pd.DataFrame):
                            self.current_df = result_df
                            self._file_modified = True
                            result += f"已更新数据集，新大小: {self.current_df.shape[0]}行 x {self.current_df.shape[1]}列\n"
                            result += f"数据预览:\n{self.current_df.head(5).to_string()}\n"
                        elif result_df is not None:
                            result += f"{result_df}\n"
                    
                    # 获取修复后的打印输出
                    fixed_output = redirected_output.getvalue()
                    if fixed_output:
                        result += "代码输出:\n" + fixed_output
                except Exception as fix_e:
                    result += f"修复失败: {str(fix_e)}\n"
            
            # 添加调试信息
            result += f"\n调试信息:\n执行的代码:\n{code}\n"
        finally:
            # 恢复标准输出
            sys.stdout = old_stdout
            
        return result
        
    def _try_fix_code(self, code, error_msg):
        """尝试修复代码中的常见错误"""
        fixed_code = code
        
        # 修复索引错误 '[x] not found in axis'
        if "not found in axis" in error_msg:
            # 提取错误的索引
            match = re.search(r'\[([^\]]+)\] not found in axis', error_msg)
            if match:
                index = match.group(1)
                # 尝试在loc/iloc前添加条件检查
                fixed_code = re.sub(
                    rf'\.loc\[{re.escape(index)}\]', 
                    f".loc[{index}] if {index} in self.current_df.index else None", 
                    fixed_code
                )
                fixed_code = re.sub(
                    rf'\.iloc\[{re.escape(index)}\]', 
                    f".iloc[{index}] if len(self.current_df) > {index} else None", 
                    fixed_code
                )
                
                # 检查是否是删除行操作
                if "drop" in fixed_code and "index" in fixed_code:
                    # 将drop操作修改为使用reset_index和query的组合
                    fixed_code = re.sub(
                        r'drop\s*\(\s*index\s*=\s*([^)]+)\s*\)',
                        r'query("index != @\1", engine="python") if \1 in self.current_df.index else self.current_df.copy()',
                        fixed_code
                    )
        
        # 修复列名错误
        if "KeyError:" in error_msg:
            # 提取错误的列名
            match = re.search(r"KeyError: ['\"]([^'\"]+)['\"]", error_msg)
            if match:
                col_name = match.group(1)
                # 检查是否是列名拼写错误
                similar_cols = self._find_similar_columns(col_name)
                if similar_cols:
                    closest_col = similar_cols[0]
                    fixed_code = re.sub(
                        rf"['\"]({re.escape(col_name)})['\"]", 
                        f"'{closest_col}'", 
                        fixed_code
                    )
        
        # 修复语法错误
        if "SyntaxError:" in error_msg:
            # 尝试简单的语法修复
            if "unexpected EOF" in error_msg or "expected" in error_msg:
                # 检查括号是否匹配
                if fixed_code.count('(') > fixed_code.count(')'):
                    fixed_code += ')' * (fixed_code.count('(') - fixed_code.count(')'))
                if fixed_code.count('[') > fixed_code.count(']'):
                    fixed_code += ']' * (fixed_code.count('[') - fixed_code.count(']'))
                    
        # 如果代码没有变化，表示无法修复，返回None
        if fixed_code == code:
            return None
            
        return fixed_code
        
    def _find_similar_columns(self, col_name):
        """查找与给定列名相似的列名"""
        if self.current_df is None:
            return []
            
        # 简单的相似度计算 - 可以改进为Levenshtein距离
        similarities = []
        for col in self.current_df.columns:
            col_str = str(col)
            # 计算共同字符的比例
            common = sum(c1 == c2 for c1, c2 in zip(col_name, col_str))
            similarity = common / max(len(col_name), len(col_str))
            similarities.append((col_str, similarity))
            
        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        # 返回相似度大于0.5的列名
        return [col for col, sim in similarities if sim > 0.5]

    def _preprocess_code(self, code):
        """预处理代码，确保可以安全执行并提高准确率"""
        # 1. 替换常见变量名为self.current_df
        common_df_names = [r'\bdf\b', r'\bdata\b', r'\bdataframe\b', r'\bexcel_data\b', r'\bcsv_data\b']
        for var_pattern in common_df_names:
            code = re.sub(var_pattern + r'(?!\s*=)', 'self.current_df', code)

        # 2. 添加数据验证检查
        validation_checks = []
        if 'self.current_df' in code:
            validation_checks.append("# 数据验证检查")
            validation_checks.append("if self.current_df is None or self.current_df.empty:")
            validation_checks.append("    raise ValueError('数据为空，无法执行操作')")
            validation_checks.append("")

        # 3. 处理列名访问安全性
        # 查找所有列名引用模式
        column_patterns = [
            r"self\.current_df\[(['\"])([^'\"]+)\1\]",  # self.current_df['column']
            r"self\.current_df\.([a-zA-Z_][a-zA-Z0-9_]*)",  # self.current_df.column
        ]

        found_columns = set()
        for pattern in column_patterns:
            matches = re.findall(pattern, code)
            if pattern.endswith(r"([a-zA-Z_][a-zA-Z0-9_]*)"):  # 点号访问
                found_columns.update(matches)
            else:  # 括号访问
                found_columns.update([match[1] for match in matches])

        # 为找到的列添加存在性检查
        for column in found_columns:
            if column not in ['shape', 'columns', 'index', 'dtypes', 'head', 'tail', 'info', 'describe']:
                check_line = f"if '{column}' not in self.current_df.columns:"
                error_line = f"    raise KeyError(f'列 \"{column}\" 不存在，可用列: {{list(self.current_df.columns)}}')"
                validation_checks.extend([check_line, error_line])

        # 4. 处理常见的索引错误模式
        code = re.sub(r'\.iloc\[0\]', '.iloc[0] if len(self.current_df) > 0 else None', code)
        code = re.sub(r'\.loc\[0\]', '.loc[0] if 0 in self.current_df.index else None', code)
        code = re.sub(r'\.iloc\[(\d+)\]', r'.iloc[\1] if len(self.current_df) > \1 else None', code)

        # 5. 处理数据类型转换的安全性
        code = re.sub(r'pd\.to_numeric\(([^)]+)\)', r'pd.to_numeric(\1, errors="coerce")', code)
        code = re.sub(r'\.astype\(([^)]+)\)', r'.astype(\1, errors="ignore")', code)

        # 6. 处理分组操作的安全性
        if '.groupby(' in code:
            validation_checks.append("# 分组操作前检查")
            validation_checks.append("# 确保分组列存在且不全为空值")

        # 7. 自动添加结果赋值
        if not re.search(r'result\s*=', code):
            lines = code.strip().split('\n')
            last_line = lines[-1].strip()

            # 检查最后一行是否是一个表达式而非赋值或控制语句
            if (last_line and
                not re.search(r'=', last_line) and
                not last_line.startswith(('if', 'for', 'while', 'def', 'class', 'return', 'raise', 'print', '#')) and
                not last_line.endswith(':')):
                # 将最后一行替换为赋值给result
                lines[-1] = f"result = {last_line}"
                code = '\n'.join(lines)

        # 8. 添加必要的导入语句
        imports = []
        if 'pd.' in code and 'import pandas' not in code:
            imports.append('import pandas as pd')
        if 'np.' in code and 'import numpy' not in code:
            imports.append('import numpy as np')
        if 'plt.' in code and 'import matplotlib' not in code:
            imports.append('import matplotlib.pyplot as plt')
        if 're.' in code and 'import re' not in code:
            imports.append('import re')

        # 9. 组装最终代码
        final_code_parts = []
        if imports:
            final_code_parts.extend(imports)
            final_code_parts.append("")

        if validation_checks:
            final_code_parts.extend(validation_checks)
            final_code_parts.append("")

        final_code_parts.append(code)

        # 10. 添加执行后的结果验证
        final_code_parts.extend([
            "",
            "# 结果验证",
            "if 'result' in locals():",
            "    if hasattr(result, 'shape'):",
            "        print(f'操作完成，结果形状: {result.shape}')",
            "    elif hasattr(result, '__len__'):",
            "        print(f'操作完成，结果长度: {len(result)}')",
            "    else:",
            "        print(f'操作完成，结果: {result}')"
        ])

        return '\n'.join(final_code_parts)

    def _clear_chat_history(self, history_text):
        """清空AI对话历史"""
        # 清空文本区域
        history_text.delete("1.0", tk.END)
        history_text.insert("1.0", "系统: 对话历史已清空\n", "system")
        
        # 重置全局变量，确保清空生成的代码
        self.result_frame = None
        
        # 恢复到原始数据
        if self._original_df is not None and self.current_df is not None:
            self.current_df = self._original_df.copy()
            self._file_modified = False
        
        # 重新添加系统提示信息，保持与初始化时相同
        history_text.insert("end", "系统: 成功连接到Ollama服务\n", "system")
        
        # 添加当前数据集信息
        if self.current_df is not None:
            df_info = (f"当前数据集: {self.current_path}\n"
                      f"行数: {self.current_df.shape[0]}, 列数: {self.current_df.shape[1]}\n"
                      f"列名: {', '.join(str(col) for col in self.current_df.columns)}")
            history_text.insert("end", f"系统: {df_info}\n", "system")
            history_text.insert("end", "系统: 请输入您想对数据做什么操作，例如: \n- '删除所有缺失值的行' \n- '按年龄分组计算平均值' \n- '找出销售额最高的前5个产品' \n- '创建一个年龄与收入的散点图'\n", "system")
            history_text.insert("end", "系统: 如果代码执行出错，系统会自动尝试修复常见问题\n", "system")
            history_text.insert("end", "系统: 若结果不符合预期，点击清空对话恢复到原始数据，若结果符合预期，点击保存数据覆盖原始文件\n", "system")

        history_text.see("end")

    def _save_system_prompt(self, system_text):
        """保存系统提示为默认值"""
        prompt = system_text.get("1.0", "end-1c").strip()
        if prompt:
            self.config["system_prompt"] = prompt
            if save_config(self.config):
                messagebox.showinfo("成功", "系统提示已保存为默认设置")
            else:
                messagebox.showerror("错误", "保存配置失败")

    def _connect_to_server(self, server_url, history_text, model_cb):
        """连接到指定的Ollama服务器"""
        global OLLAMA_SERVER
        if not server_url:
            history_text.insert("end", "系统: 请输入有效的服务器地址\n", "error")
            return
            
        # 更新服务器地址
        old_url = self.ollama.base_url
        self.ollama.base_url = server_url
        self.ollama.api_url = f"{server_url}/api/generate"
        
        # 测试连接
        history_text.insert("end", f"系统: 正在连接到 {server_url}...\n", "system")
        self.ollama.available = self.ollama._check_availability()
        
        if self.ollama.available:
            history_text.insert("end", f"系统: 成功连接到 {server_url}\n", "system")
            # 刷新模型列表
            self._refresh_models(model_cb, history_text)
            
            # 保存配置
            self.config["ollama_server"] = server_url
            OLLAMA_SERVER = server_url  # 更新全局变量
            save_config(self.config)
        else:
            history_text.insert("end", f"系统: 无法连接到 {server_url}\n", "error")
            # 恢复原来的URL
            self.ollama.base_url = old_url
            self.ollama.api_url = f"{old_url}/api/generate"

    def _create_enhanced_table_view(self, df, title="数据预览", max_rows=10, max_cols=None):
        """创建增强的表格视图，使用HTML样式的表格格式

        参数:
            df (pandas.DataFrame): 要格式化的DataFrame
            title (str): 表格标题
            max_rows (int): 最大显示行数
            max_cols (int): 最大显示列数，None表示自动决定

        返回:
            str: 格式化后的字符串
        """
        if df is None:
            return "📭 无数据可显示"

        try:
            # 限制显示的行数和列数
            display_df = df.head(max_rows)
            if max_cols and len(df.columns) > max_cols:
                display_df = display_df.iloc[:, :max_cols]
                truncated_cols = True
            else:
                truncated_cols = False

            # 创建表格内容
            lines = []

            # 表格标题区域
            title_line = f"📊 {title} ({df.shape[0]} 行 × {df.shape[1]} 列)"
            lines.append("╔" + "═" * (len(title_line) + 2) + "╗")
            lines.append(f"║ {title_line} ║")
            lines.append("╠" + "═" * (len(title_line) + 2) + "╣")

            # 处理列名和数据
            processed_data = []
            col_names = []
            col_widths = []

            for i, col in enumerate(display_df.columns):
                col_str = str(col)
                # 限制列名长度
                if len(col_str) > 12:
                    col_str = col_str[:9] + "..."
                col_names.append(col_str)

                # 计算列宽度
                max_width = len(col_str)
                col_data = []

                for idx in display_df.index:
                    cell_value = str(display_df.iloc[idx, i])
                    # 处理特殊值
                    if pd.isna(display_df.iloc[idx, i]):
                        cell_value = "∅"  # 空值符号
                    elif cell_value == "":
                        cell_value = "━"  # 空字符串
                    elif len(cell_value) > 15:
                        cell_value = cell_value[:12] + "..."

                    col_data.append(cell_value)
                    max_width = max(max_width, len(cell_value))

                # 设置合理的列宽
                col_width = min(max(max_width, 6), 18)
                col_widths.append(col_width)
                processed_data.append(col_data)

            # 构建表头
            header_parts = []
            for i, (name, width) in enumerate(zip(col_names, col_widths)):
                header_parts.append(f"{name:^{width}}")

            header_line = "║ " + " ┃ ".join(header_parts) + " ║"
            lines.append(header_line)

            # 表头分隔线
            sep_parts = []
            for width in col_widths:
                sep_parts.append("━" * width)
            sep_line = "╠━" + "━╋━".join(sep_parts) + "━╣"
            lines.append(sep_line)

            # 数据行
            for row_idx in range(len(display_df)):
                row_parts = []
                for col_idx, width in enumerate(col_widths):
                    cell_value = processed_data[col_idx][row_idx]

                    # 根据数据类型调整对齐
                    if pd.api.types.is_numeric_dtype(display_df.iloc[:, col_idx]) and cell_value not in ["∅", "━"]:
                        # 数字右对齐
                        formatted_cell = f"{cell_value:>{width}}"
                    else:
                        # 文本居中对齐
                        formatted_cell = f"{cell_value:^{width}}"

                    row_parts.append(formatted_cell)

                row_line = "║ " + " ┃ ".join(row_parts) + " ║"
                lines.append(row_line)

            # 表格底部
            bottom_parts = []
            for width in col_widths:
                bottom_parts.append("═" * width)
            bottom_line = "╚═" + "═╩═".join(bottom_parts) + "═╝"
            lines.append(bottom_line)

            # 添加数据统计信息
            lines.append("")
            lines.append("📈 数据概览:")
            lines.append("┌" + "─" * 50 + "┐")

            # 数据类型统计
            dtype_info = []
            dtype_counts = df.dtypes.value_counts()
            for dtype, count in dtype_counts.items():
                dtype_name = str(dtype).replace('object', '文本').replace('int64', '整数').replace('float64', '小数')
                dtype_info.append(f"{dtype_name}: {count}列")

            lines.append(f"│ 🏷️  数据类型: {', '.join(dtype_info):<35} │")

            # 缺失值统计
            missing_total = df.isnull().sum().sum()
            missing_percent = (missing_total / (df.shape[0] * df.shape[1]) * 100) if df.shape[0] > 0 else 0

            if missing_total > 0:
                lines.append(f"│ ⚠️  缺失值: {missing_total}个 ({missing_percent:.1f}%){'':>20} │")
            else:
                lines.append(f"│ ✅ 数据完整: 无缺失值{'':>30} │")

            # 显示范围信息
            if len(df) > max_rows or truncated_cols:
                range_info = []
                if len(df) > max_rows:
                    range_info.append(f"显示前{max_rows}行")
                if truncated_cols:
                    range_info.append(f"显示前{max_cols}列")
                lines.append(f"│ 📄 显示范围: {', '.join(range_info):<32} │")

            lines.append("└" + "─" * 50 + "┘")

            return "\n".join(lines)

        except Exception as e:
            # 如果增强格式失败，使用简化版本
            return self._create_simple_table_view(df, title, max_rows, max_cols, error=str(e))



    def _create_streamlit_preview(self, df, title="数据预览", max_rows=10, max_cols=None):
        """使用streamlit风格创建数据预览

        参数:
            df (pandas.DataFrame): 要格式化的DataFrame
            title (str): 表格标题
            max_rows (int): 最大显示行数
            max_cols (int): 最大显示列数，None表示自动决定

        返回:
            str: 格式化后的字符串
        """
        if df is None:
            return "📭 无数据可显示"

        try:
            import io
            import sys

            # 限制显示的行数和列数
            display_df = df.head(max_rows)
            if max_cols and len(df.columns) > max_cols:
                display_df = display_df.iloc[:, :max_cols]
                truncated_cols = True
            else:
                truncated_cols = False

            # 创建streamlit风格的输出
            lines = []

            # 标题部分
            lines.append("🎯 " + "=" * 80)
            lines.append(f"📊 {title}")
            lines.append(f"📈 数据维度: {df.shape[0]} 行 × {df.shape[1]} 列")
            lines.append("=" * 80)
            lines.append("")

            # 使用streamlit的dataframe显示风格
            # 创建一个临时的StringIO来捕获streamlit风格的输出
            buffer = io.StringIO()

            # 模拟streamlit的dataframe显示
            lines.append("📋 数据表格:")
            lines.append("─" * 60)

            # 使用pandas的to_string方法，但配置为streamlit风格
            with pd.option_context(
                'display.max_columns', max_cols or len(display_df.columns),
                'display.width', 120,
                'display.max_colwidth', 20,
                'display.precision', 3,
                'display.float_format', '{:.3f}'.format
            ):
                # 创建表格内容
                table_str = display_df.to_string(
                    index=True,
                    na_rep='∅',
                    justify='center',
                    col_space=2
                )

                # 添加边框和美化
                table_lines = table_str.split('\n')
                for i, line in enumerate(table_lines):
                    if i == 0:  # 表头
                        lines.append("┌" + "─" * (len(line) + 2) + "┐")
                        lines.append("│ " + line + " │")
                        lines.append("├" + "─" * (len(line) + 2) + "┤")
                    elif i == 1:  # 分隔线，跳过
                        continue
                    else:  # 数据行
                        lines.append("│ " + line + " │")

                if table_lines:
                    lines.append("└" + "─" * (len(table_lines[0]) + 2) + "┘")

            lines.append("")

            # 数据统计信息 - streamlit风格
            lines.append("📊 数据统计:")
            lines.append("─" * 40)

            # 数据类型信息
            dtype_counts = df.dtypes.value_counts()
            lines.append("🏷️ 数据类型分布:")
            for dtype, count in dtype_counts.items():
                dtype_name = str(dtype).replace('object', '文本').replace('int64', '整数').replace('float64', '小数')
                lines.append(f"   • {dtype_name}: {count} 列")

            # 缺失值统计
            missing_total = df.isnull().sum().sum()
            missing_percent = (missing_total / (df.shape[0] * df.shape[1]) * 100) if df.shape[0] > 0 else 0

            lines.append("")
            lines.append("⚠️ 数据质量:")
            if missing_total > 0:
                lines.append(f"   • 缺失值: {missing_total} 个 ({missing_percent:.1f}%)")
                # 显示缺失值最多的列
                missing_by_col = df.isnull().sum()
                missing_by_col = missing_by_col[missing_by_col > 0].sort_values(ascending=False)
                if len(missing_by_col) > 0:
                    lines.append("   • 缺失值分布:")
                    for col, count in missing_by_col.head(3).items():
                        col_name = str(col)[:15] + "..." if len(str(col)) > 15 else str(col)
                        lines.append(f"     - {col_name}: {count} 个")
            else:
                lines.append("   • ✅ 数据完整，无缺失值")

            # 数值列统计
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                lines.append("")
                lines.append("📈 数值列概览:")
                for col in numeric_cols[:3]:  # 只显示前3个数值列
                    col_data = df[col].dropna()
                    if len(col_data) > 0:
                        lines.append(f"   • {col}: 均值={col_data.mean():.2f}, 范围=[{col_data.min():.2f}, {col_data.max():.2f}]")

            # 显示范围信息
            if len(df) > max_rows or truncated_cols:
                lines.append("")
                lines.append("📄 显示说明:")
                if len(df) > max_rows:
                    lines.append(f"   • 仅显示前 {max_rows} 行数据")
                if truncated_cols:
                    lines.append(f"   • 仅显示前 {max_cols} 列数据")

            lines.append("")
            lines.append("=" * 80)

            return "\n".join(lines)

        except Exception as e:
            # 如果streamlit风格失败，回退到简化格式
            return self._create_simple_table_view(df, title, max_rows, max_cols, error=str(e))

    def _create_simple_table_view(self, df, title="数据预览", max_rows=10, max_cols=None, error=None):
        """创建简化的表格视图作为备用方案"""
        if df is None:
            return "📭 无数据可显示"

        try:
            lines = []

            # 错误信息
            if error:
                lines.append(f"⚠️ Streamlit风格显示失败，使用简化格式: {error}")
                lines.append("")

            # 标题
            lines.append("=" * 60)
            lines.append(f"📊 {title}")
            lines.append(f"📈 数据维度: {df.shape[0]} 行 × {df.shape[1]} 列")
            lines.append("=" * 60)

            # 限制显示的行数和列数
            display_df = df.head(max_rows)
            if max_cols and len(df.columns) > max_cols:
                display_df = display_df.iloc[:, :max_cols]
                truncated_cols = True
            else:
                truncated_cols = False

            # 简单的表格显示
            lines.append("")
            lines.append("📋 数据表格:")
            lines.append("-" * 40)

            # 使用pandas的简单to_string
            table_str = display_df.to_string(index=True, na_rep='∅')
            lines.append(table_str)

            # 显示范围信息
            if len(df) > max_rows or truncated_cols:
                lines.append("")
                lines.append("📄 显示说明:")
                if len(df) > max_rows:
                    lines.append(f"   • 仅显示前 {max_rows} 行数据")
                if truncated_cols:
                    lines.append(f"   • 仅显示前 {max_cols} 列数据")

            lines.append("")
            lines.append("=" * 60)

            return "\n".join(lines)

        except Exception as e2:
            return f"❌ 数据显示失败: {str(e2)}\n数据形状: {df.shape if df is not None else 'None'}"

    def _format_dataframe_preview(self, df, max_rows=10, max_cols=None):
        """格式化DataFrame预览的主入口方法 - 使用streamlit风格"""
        return self._create_streamlit_preview(df, "数据预览", max_rows, max_cols)

    def _create_streamlit_app_file(self, df, title="数据预览", app_type="preview"):
        """创建临时的streamlit应用文件"""
        if df is None:
            return None, None

        # 创建临时文件
        temp_dir = tempfile.gettempdir()
        app_file = os.path.join(temp_dir, f"streamlit_{app_type}_{int(time.time())}.py")
        data_file = os.path.join(temp_dir, f"data_{app_type}_{int(time.time())}.pkl")

        # 保存数据到临时文件
        df.to_pickle(data_file)

        # 创建streamlit应用代码
        app_code = f'''
import streamlit as st
import pandas as pd
import numpy as np
import os
import io

st.set_page_config(
    page_title="{title}",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 加载数据
@st.cache_data
def load_data():
    return pd.read_pickle(r"{data_file}")

try:
    df = load_data()

    # 主标题
    st.title("📊 {title}")
    st.markdown("---")

    # 数据概览
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("总行数", df.shape[0])
    with col2:
        st.metric("总列数", df.shape[1])
    with col3:
        st.metric("缺失值", df.isnull().sum().sum())
    with col4:
        memory_usage = df.memory_usage(deep=True).sum() / 1024 / 1024
        st.metric("内存使用", f"{{memory_usage:.2f}} MB")

    st.markdown("---")

    # 侧边栏控制
    st.sidebar.header("显示控制")
    show_rows = st.sidebar.slider("显示行数", 5, min(100, len(df)), min(20, len(df)))
    show_cols = st.sidebar.multiselect("选择列", df.columns.tolist(), default=df.columns.tolist()[:10])

    if not show_cols:
        show_cols = df.columns.tolist()[:5]

    # 数据表格
    st.subheader("📋 数据表格")
    display_df = df[show_cols].head(show_rows)
    st.dataframe(display_df, use_container_width=True, height=400)

    # 数据统计
    st.subheader("📈 数据统计")

    # 数据类型
    col1, col2 = st.columns(2)
    with col1:
        st.write("**数据类型分布**")
        dtype_counts = df.dtypes.value_counts()
        st.bar_chart(dtype_counts)

    with col2:
        st.write("**缺失值统计**")
        missing_data = df.isnull().sum()
        missing_data = missing_data[missing_data > 0]
        if len(missing_data) > 0:
            st.bar_chart(missing_data)
        else:
            st.success("✅ 无缺失值")

    # 数值列统计
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    if len(numeric_cols) > 0:
        st.subheader("🔢 数值列统计")
        selected_numeric_cols = st.multiselect("选择数值列", numeric_cols.tolist(), default=numeric_cols.tolist()[:3])
        if selected_numeric_cols:
            st.write(df[selected_numeric_cols].describe())

    # 文本列统计
    text_cols = df.select_dtypes(include=['object', 'string']).columns
    if len(text_cols) > 0:
        st.subheader("📝 文本列统计")
        selected_text_col = st.selectbox("选择文本列", text_cols.tolist())
        if selected_text_col:
            value_counts = df[selected_text_col].value_counts().head(10)
            st.bar_chart(value_counts)

    # 数据下载
    st.sidebar.markdown("---")
    st.sidebar.subheader("数据下载")

    # CSV下载
    csv = df.to_csv(index=False).encode('utf-8-sig')
    st.sidebar.download_button(
        label="下载为CSV",
        data=csv,
        file_name=f'{{title}}_{{pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")}}.csv',
        mime='text/csv'
    )

    # Excel下载
    excel_buffer = io.BytesIO()
    with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Data')
    excel_data = excel_buffer.getvalue()

    st.sidebar.download_button(
        label="下载为Excel",
        data=excel_data,
        file_name=f'{{title}}_{{pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")}}.xlsx',
        mime='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

except Exception as e:
    st.error(f"加载数据时出错: {{str(e)}}")
    st.info("请确保数据文件存在且格式正确")

# 清理提示
st.sidebar.markdown("---")
st.sidebar.info("💡 提示：关闭浏览器标签页即可退出预览")
'''

        # 写入应用文件
        with open(app_file, 'w', encoding='utf-8') as f:
            f.write(app_code)

        return app_file, data_file

    def _launch_streamlit_preview(self, df, title="数据预览", app_type="preview"):
        """启动streamlit数据预览"""
        if df is None:
            self.update_chat("❌ 没有数据可预览")
            return

        try:
            # 停止之前的streamlit进程
            self._stop_streamlit_preview()

            # 创建streamlit应用
            app_file, data_file = self._create_streamlit_app_file(df, title, app_type)
            if not app_file:
                self.update_chat("❌ 创建预览应用失败")
                return

            # 保存文件路径以便清理
            self.temp_data_file = data_file

            # 启动streamlit
            cmd = [
                "streamlit", "run", app_file,
                "--server.port", str(self.streamlit_port),
                "--server.headless", "true",
                "--browser.gatherUsageStats", "false",
                "--server.enableCORS", "false"
            ]

            self.streamlit_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == 'win32' else 0
            )

            # 等待streamlit启动
            time.sleep(3)

            # 打开浏览器
            url = f"http://localhost:{self.streamlit_port}"
            webbrowser.open(url)

            self.update_chat(f"✅ {title}已在浏览器中打开: {url}")
            self.update_chat("💡 关闭浏览器标签页即可退出预览")

        except Exception as e:
            self.update_chat(f"❌ 启动预览失败: {str(e)}")

    def _stop_streamlit_preview(self):
        """停止streamlit预览进程"""
        if self.streamlit_process:
            try:
                self.streamlit_process.terminate()
                self.streamlit_process.wait(timeout=5)
            except:
                try:
                    self.streamlit_process.kill()
                except:
                    pass
            self.streamlit_process = None

        # 清理临时文件
        if self.temp_data_file and os.path.exists(self.temp_data_file):
            try:
                os.remove(self.temp_data_file)
            except:
                pass
            self.temp_data_file = None

    def _preview_ai_operation_with_streamlit(self, input_text, history_text, system_text, preview_text):
        """AI操作预览（合并原预览功能和streamlit预览显示）"""
        # 首先执行原有的预览操作
        self._preview_ai_operation(input_text, history_text, system_text, preview_text)

        # 如果预览结果存在，则使用streamlit显示
        if self.preview_result_df is not None:
            # 使用streamlit显示预览结果
            self._launch_streamlit_preview(self.preview_result_df, "AI操作预览结果", "ai_preview")
        else:
            self.update_chat("❌ 预览操作未生成结果数据")

    def _on_closing(self):
        """应用关闭时的清理工作"""
        # 停止streamlit进程
        self._stop_streamlit_preview()
        # 关闭主窗口
        self.root.destroy()

    def update_chat(self, message):
        """更新聊天/日志区域的内容，使用增强的显示格式"""
        self.chat_display.config(state="normal")

        # 检查是否包含DataFrame对象，如果是则使用增强表格格式
        if hasattr(self, 'current_df') and self.current_df is not None and "数据预览:" in message:
            try:
                # 使用streamlit风格显示当前数据
                streamlit_preview = self._create_streamlit_preview(
                    self.current_df,
                    "当前数据集",
                    max_rows=8,
                    max_cols=8
                )

                # 添加时间戳和美化的分隔线
                import datetime
                timestamp = datetime.datetime.now().strftime("%H:%M:%S")

                self.chat_display.insert(tk.END, "\n" + "═" * 80 + "\n", "separator")
                self.chat_display.insert(tk.END, f"🕒 {timestamp} - 数据加载完成\n", "timestamp")
                self.chat_display.insert(tk.END, "═" * 80 + "\n", "separator")
                self.chat_display.insert(tk.END, streamlit_preview + "\n", "streamlit_table")
                self.chat_display.insert(tk.END, "═" * 80 + "\n\n", "separator")

            except Exception as e:
                # 如果增强格式失败，使用原始消息
                self.chat_display.insert(tk.END, f"⚠️ 表格格式化失败: {str(e)}\n")
                self.chat_display.insert(tk.END, message + "\n")

        elif "数据预览:" in message or "预览:" in message:
            # 处理其他类型的预览消息
            parts = message.split("\n", 1)
            header = parts[0]

            # 插入美化的标题框
            self.chat_display.insert(tk.END, "\n" + "╔" + "═" * 78 + "╗\n", "header_border")
            self.chat_display.insert(tk.END, f"║ 📊 {header:<74} ║\n", "header_text")
            self.chat_display.insert(tk.END, "╚" + "═" * 78 + "╝\n", "header_border")

            # 处理数据部分
            if len(parts) > 1:
                data = parts[1]
                # 使用等宽字体显示数据部分
                self.chat_display.insert(tk.END, data + "\n\n", "data_content")
            else:
                self.chat_display.insert(tk.END, "\n")

        elif "列名:" in message and "," in message:
            # 美化列名显示
            header, columns = message.split(":", 1)
            columns_list = columns.strip().split(", ")

            self.chat_display.insert(tk.END, "\n┌" + "─" * 60 + "┐\n", "column_border")
            self.chat_display.insert(tk.END, f"│ 📋 数据列信息 (共 {len(columns_list)} 列)".ljust(61) + "│\n", "column_header")
            self.chat_display.insert(tk.END, "├" + "─" * 60 + "┤\n", "column_border")

            # 每行显示4个列名，使列名显示更整洁
            for i in range(0, len(columns_list), 4):
                chunk = columns_list[i:i+4]
                formatted_line = "│ " + " • ".join(f"{col:<12}" for col in chunk)
                # 补齐到固定宽度
                formatted_line = formatted_line.ljust(61) + "│\n"
                self.chat_display.insert(tk.END, formatted_line, "column_content")

            self.chat_display.insert(tk.END, "└" + "─" * 60 + "┘\n\n", "column_border")

        else:
            # 普通消息的美化处理
            if message.startswith("系统:"):
                # 系统消息
                self.chat_display.insert(tk.END, "🔧 ", "system_icon")
                self.chat_display.insert(tk.END, message + "\n", "system_message")
            elif message.startswith("错误:") or "失败" in message or "错误" in message:
                # 错误消息
                self.chat_display.insert(tk.END, "❌ ", "error_icon")
                self.chat_display.insert(tk.END, message + "\n", "error_message")
            elif "成功" in message or "完成" in message or "已保存" in message:
                # 成功消息
                self.chat_display.insert(tk.END, "✅ ", "success_icon")
                self.chat_display.insert(tk.END, message + "\n", "success_message")
            elif "已加载" in message or "数据集" in message:
                # 数据加载消息
                self.chat_display.insert(tk.END, "📂 ", "load_icon")
                self.chat_display.insert(tk.END, message + "\n", "load_message")
            else:
                # 普通消息
                self.chat_display.insert(tk.END, message + "\n")

        # 处理完毕，禁用编辑并滚动到底部
        self.chat_display.config(state="disabled")

        # 确保滚动到最新内容
        self.chat_display.see(tk.END)

        # 强制更新UI以确保滚动条正确显示
        self.chat_display.update_idletasks()

    def run_special_op(self, op_type):
        """直接运行特定操作，不再显示特殊操作菜单"""
        if self.current_df is None:
            self.update_chat("请先加载数据文件")
            return
            
        # 创建操作窗口
        win = tk.Toplevel(self.root)
        win.title(self.get_op_title(op_type))
        win.geometry("850x550")  # 增大窗口宽度，使用更多空白区域
        win.minsize(750, 450)    # 增大最小尺寸
        win.resizable(True, True)
        
        # 创建主布局框架
        main_frame = ttk.Frame(win)
        main_frame.pack(fill="both", expand=True)
        
        # 创建左侧框架用于放置按钮
        left_frame = ttk.Frame(main_frame, width=100)
        left_frame.pack(side="left", fill="y", padx=10, pady=10)
        
        # 创建右侧框架用于放置内容区域
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side="left", fill="both", expand=True, padx=5, pady=10)
        
        # 创建按钮区域 - 竖向排列
        btn_frame = ttk.Frame(left_frame)
        btn_frame.pack(anchor="n", pady=10)
        
        preview_btn = ttk.Button(btn_frame, text="预览", width=10)
        preview_btn.pack(pady=5)
        
        execute_btn = ttk.Button(btn_frame, text="保存预览", width=10)
        execute_btn.pack(pady=5)
        
        # 修改关闭按钮的行为，清除预览结果
        close_btn = ttk.Button(btn_frame, text="关闭", width=10, 
                              command=lambda: self._close_special_op(win))
        close_btn.pack(pady=5)
        
        # 创建主滚动区域
        main_canvas = tk.Canvas(right_frame)
        scrollbar = ttk.Scrollbar(right_frame, orient="vertical", command=main_canvas.yview)
        scrollable_frame = ttk.Frame(main_canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: main_canvas.configure(scrollregion=main_canvas.bbox("all"))
        )
        
        main_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        main_canvas.configure(yscrollcommand=scrollbar.set)
        
        # 参数区
        param_frame = ttk.LabelFrame(scrollable_frame, text="参数设置")
        param_frame.pack(fill="both", expand=False, padx=10, pady=5)

        # 预览区
        preview_frame = ttk.LabelFrame(scrollable_frame, text="预览区")
        preview_frame.pack(fill="both", expand=True, padx=5, pady=5)
        preview_text = scrolledtext.ScrolledText(preview_frame, height=10)
        preview_text.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 将主画布和滚动条放入窗口
        main_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 设置参数控件和操作函数
        widgets = {}
        self.setup_param_ui(op_type, param_frame, widgets)
        
        # 添加预览结果变量
        preview_result = None  # 保存预览结果
        
        def preview_operation(text_widget):
            """预览操作结果但不实际执行"""
            nonlocal preview_result  # 引用外部变量
            text_widget.delete("1.0", tk.END)
            try:
                df, desc = self.execute_special_op(op_type, widgets)
                text_widget.insert(tk.END, desc+"\n")
                # 使用streamlit风格显示预览
                streamlit_preview = self._create_streamlit_preview(df, "操作预览", max_rows=10, max_cols=8)
                text_widget.insert(tk.END, streamlit_preview)
                # 保存预览结果供执行时使用
                preview_result = df.copy()
            except Exception as e:
                text_widget.insert(tk.END, f"预览失败: {e}\n")
                traceback.print_exc()
                preview_result = None
                
        def execute_operation():
            """执行操作并更新当前DataFrame"""
            nonlocal preview_result  # 引用外部变量
            try:
                if preview_result is not None:
                    # 使用预览结果，避免重新执行代码
                    df = preview_result
                    # 获取操作描述信息
                    desc = f"已使用预览结果更新数据 (操作类型: {op_type})"
                else:
                    # 如果没有预览结果，则重新执行
                    df, desc = self.execute_special_op(op_type, widgets)
                
                self.current_df = df
                self._file_modified = True

                # 如果有原始文件，询问保存方式
                if self.current_path and self._file_modified:
                    # 显示保存选项对话框
                    save_option = self._show_save_options_dialog("保存操作结果")
                    if save_option == 'overwrite':
                        # 覆盖原文件
                        save_result = self._save_to_original_file()
                        if save_result["success"]:
                            self.update_chat(save_result["message"])
                        else:
                            self.update_chat(f"保存失败: {save_result['message']}")
                    elif save_option == 'save_as':
                        # 另存为
                        save_result = self._save_as_new_file()
                        if save_result["success"]:
                            self.update_chat(save_result["message"])
                        else:
                            self.update_chat(f"保存失败: {save_result['message']}")
                    else:
                        # 用户取消保存
                        self.update_chat("用户取消保存，数据仅在内存中更新")
                
                self.update_chat(desc)
                # 使用改进的数据格式化方法显示预览
                preview = self._format_dataframe_preview(df, max_rows=12)
                self.update_chat("预览:\n" + preview)
                win.destroy()
            except Exception as e:
                self.update_chat(f"操作失败: {e}")
                traceback.print_exc()
                
        # 设置按钮功能
        preview_btn.configure(command=lambda: preview_operation(preview_text))
        execute_btn.configure(command=lambda: execute_operation())

    def get_op_title(self, op_type):
        """获取操作的标题"""
        titles = {
            "delete_rows": "删除满足条件的行",
            "replace_values": "替换特定值",
            "convert_type": "转换列数据类型",
            "handle_missing": "处理缺失值",
            "create_column": "创建新列",
            "pivot_table": "创建数据透视表",
            "normalize_data": "数据归一化/标准化",
            "multi_filter": "多条件筛选(表达式)",
            "drop_duplicates": "删除重复行",
            "drop_blank_rows": "删除空白行",
            "ai_categorize": "AI智能分类标注",
            "similarity_analysis": "两列数据相似度分析"
        }
        return titles.get(op_type, "特殊数据操作")

    def _delete_rows(self, column, operator, value):
        df = self.current_df.copy() if self.current_df is not None else None
        if df is None:
            raise ValueError("当前没有加载数据")
        try:
            if df[column].dtype.kind in 'ifc':
                value = float(value)
        except (ValueError, TypeError):
            pass
        mask = self._apply_operator(df, column, operator, value)
        result = df[~mask]
        return result, f"已删除满足条件的行: {column} {operator} {value}"

    def _replace_values(self, column, operator, value, new_value):
        df = self.current_df.copy() if self.current_df is not None else None
        if df is None:
            raise ValueError("当前没有加载数据")
        try:
            if df[column].dtype.kind in 'ifc':
                value = float(value)
                new_value = float(new_value)
        except (ValueError, TypeError):
            pass
        mask = self._apply_operator(df, column, operator, value)
        df.loc[mask, column] = new_value
        return df, f"已将满足条件的值替换: {column} {operator} {value} -> {new_value}"

    def _convert_column_type(self, column, target_type):
        df = self.current_df.copy() if self.current_df is not None else None
        if df is None:
            raise ValueError("当前没有加载数据")
        try:
            if target_type == "int":
                df[column] = pd.to_numeric(df[column], errors='coerce').fillna(0).astype(int)
            elif target_type == "float":
                df[column] = pd.to_numeric(df[column], errors='coerce')
            elif target_type == "str":
                df[column] = df[column].astype(str)
            elif target_type == "datetime":
                df[column] = pd.to_datetime(df[column], errors='coerce')
            elif target_type == "category":
                df[column] = df[column].astype('category')
            elif target_type == "bool":
                df[column] = df[column].astype(bool)
        except Exception as e:
            raise Exception(f"类型转换失败: {str(e)}")
        return df, f"已将列 '{column}' 转换为 {target_type} 类型"

    def _handle_missing_values(self, column, method, fill_value):
        df = self.current_df.copy() if self.current_df is not None else None
        if df is None:
            raise ValueError("当前没有加载数据")
        missing_count = df[column].isna().sum()
        if method == "填充":
            try:
                if df[column].dtype.kind in 'ifc':
                    fill_value = float(fill_value)
            except (ValueError, TypeError):
                pass
            df[column] = df[column].fillna(fill_value)
            operation_desc = f"已将列 '{column}' 的 {missing_count} 个缺失值填充为 {fill_value}"
        elif method == "删除":
            df = df.dropna(subset=[column])
            operation_desc = f"已删除列 '{column}' 中包含缺失值的 {missing_count} 行数据"
        elif method == "插值":
            if pd.api.types.is_numeric_dtype(df[column]):
                df[column] = df[column].interpolate(method='linear')
                operation_desc = f"已对列 '{column}' 的 {missing_count} 个缺失值进行线性插值"
            else:
                df[column] = df[column].fillna(method='ffill')
                operation_desc = f"已对列 '{column}' 的 {missing_count} 个缺失值进行前向填充"
        return df, operation_desc

    def _create_new_column(self, base_column, expr_type, expression, new_column_name):
        df = self.current_df.copy() if self.current_df is not None else None
        if df is None:
            raise ValueError("当前没有加载数据")
        if not new_column_name:
            raise ValueError("请指定新列名称")
        if expr_type == "计算":
            safe_expr = expression
            for col in df.columns:
                pattern = r'\b' + re.escape(str(col)) + r'\b'
                safe_col = f"df['{col}']"
                safe_expr = re.sub(pattern, safe_col, safe_expr)
            result = eval(safe_expr)
            df[new_column_name] = result
            operation_desc = f"已创建新列 '{new_column_name}'，基于计算表达式: {expression}"
        elif expr_type == "条件":
            condition = expression
            for col in df.columns:
                pattern = r'\b' + re.escape(str(col)) + r'\b'
                safe_col = f"df['{col}']"
                condition = re.sub(pattern, safe_col, condition)
            df[new_column_name] = np.where(eval(condition), 1, 0)
            operation_desc = f"已创建新列 '{new_column_name}'，基于条件: {expression}"
        elif expr_type == "连接":
            # 使用逐行迭代避免Series直接加法的问题
            parts = expression.split('+')
            result = []
            for i in range(len(df)):
                row_result = ""
                for part in parts:
                    part = part.strip()
                    if part.startswith("'") and part.endswith("'"):
                        part_value = part[1:-1]
                        row_result += part_value
                    elif part.startswith('"') and part.endswith('"'):
                        part_value = part[1:-1]
                        row_result += part_value
                    elif part in df.columns:
                        row_result += str(df.iloc[i][part])
                    else:
                        raise ValueError(f"无效的表达式部分: {part}")
                result.append(row_result)
            df[new_column_name] = result
            operation_desc = f"已创建新列 '{new_column_name}'，基于连接表达式: {expression}"
        else:
            raise ValueError(f"不支持的表达式类型: {expr_type}")
        return df, operation_desc

    def _create_pivot_table(self, index, columns, values, aggregation, filter_expr=None):
        df = self.current_df.copy() if self.current_df is not None else None
        if df is None:
            raise ValueError("当前没有加载数据")
        
        # 先筛选
        if filter_expr and filter_expr.strip():
            try:
                # 预处理数据，将可能导致问题的字符串列转换为对象类型
                for col in df.columns:
                    if df[col].dtype == 'object':
                        df[col] = df[col].astype('string').fillna('')
                
                # 使用query方法筛选
                df = df.query(filter_expr, engine='python')
            except Exception as e:
                try:
                    # 尝试使用eval方法筛选
                    expr_replaced = filter_expr.replace('==', ' == ').replace('!=', ' != ').replace('>=', ' >= ').replace('<=', ' <= ')
                    expr_replaced = expr_replaced.replace('>', ' > ').replace('<', ' < ')
                    expr_replaced = expr_replaced.replace('&', ' and ').replace('|', ' or ')
                    
                    mask = eval(expr_replaced, {"__builtins__": {}}, {col: df[col] for col in df.columns})
                    mask = mask.fillna(False)  # 处理掩码中的NaN值
                    df = df[mask]
                except Exception as e2:
                    raise ValueError(f"筛选表达式有误: {str(e)}\n尝试替代方法也失败: {str(e2)}")
        
        # 解析多列
        index_list = [c.strip() for c in index.split(',') if c.strip()] if index else []
        columns_list = [c.strip() for c in columns.split(',') if c.strip()] if columns else []
        values_list = [c.strip() for c in values.split(',') if c.strip()] if values else []
        
        # 校验列名
        for col in index_list + columns_list + values_list:
            if col and col not in df.columns:
                raise ValueError(f"列名不存在: {col}")
            
        def adapt(x):
            if not x:
                return None
            if len(x) == 1:
                return x[0]
            return x
        
        idx = adapt(index_list)
        cols = adapt(columns_list)
        vals = adapt(values_list)
        
        if not idx:
            raise ValueError("请至少指定一个行标签列")
        
        agg_funcs = {
            "mean": "平均值",
            "sum": "总和",
            "count": "计数", 
            "min": "最小值",
            "max": "最大值",
            "first": "第一个值",
            "last": "最后一个值",
            "nunique": "唯一值计数"
        }
        
        try:
            # 特殊处理计数功能
            if aggregation == "count":
                # 没有指定值列时，统计每组行数
                if not vals:
                    result = pd.pivot_table(
                        df, 
                        index=idx, 
                        columns=cols, 
                        values=df.columns[0],  # 使用第一列作为计数列
                        aggfunc='count',
                        fill_value=0
                    )
                    # 重命名列，避免显示具体计数列名称
                    if isinstance(result.columns, pd.MultiIndex):
                        result.columns = result.columns.set_levels(['计数'], level=-1)
                    else:
                        result.columns = ['计数']
                # 指定值列时，统计值列非空数量
                else:
                    result = pd.pivot_table(
                        df,
                        index=idx,
                        columns=cols,
                        values=vals,
                        aggfunc='count',
                        fill_value=0
                    )
            # 唯一值计数
            elif aggregation == "nunique":
                if not vals:
                    raise ValueError("唯一值计数必须指定值列")
                result = pd.pivot_table(
                    df,
                    index=idx,
                    columns=cols,
                    values=vals,
                    aggfunc=pd.Series.nunique,
                    fill_value=0
                )
            # 其他聚合函数
            else:
                if not vals:
                    raise ValueError(f"{agg_funcs.get(aggregation, aggregation)}必须指定值列")
                result = pd.pivot_table(
                    df,
                    index=idx,
                    columns=cols,
                    values=vals,
                    aggfunc=aggregation,
                    fill_value=0
                )
            
            # 优化结果展示
            if not isinstance(result, pd.DataFrame):
                result = result.to_frame()
            
            # 重置索引，使行标签变为普通列
            result = result.reset_index()
            
            # 修复列名称（对于多级索引的情况）
            if isinstance(result.columns, pd.MultiIndex):
                result.columns = [' '.join(str(x) for x in col if x).strip() for col in result.columns.values]
            
            operation_desc = f"已创建数据透视表 - 行标签:{index}, 列标签:{columns if columns else '-'}, 值:{values if values else ('行数' if aggregation=='count' else '-')}, 聚合:{agg_funcs.get(aggregation, aggregation)}"
            
            if result.empty:
                raise ValueError("生成的数据透视表为空，请检查参数或数据")
            
            return result, operation_desc
        except Exception as e:
            raise ValueError(f"透视表创建失败: {e}")

    def _normalize_data(self, column, method):
        if not column:
            raise ValueError("请选择要处理的列")
        df = self.current_df.copy() if self.current_df is not None else None
        if df is None:
            raise ValueError("当前没有加载数据")
        if not pd.api.types.is_numeric_dtype(df[column]):
            raise ValueError(f"列 '{column}' 不是数值类型，无法进行归一化处理")
        original_column = df[column].copy()
        if method == "min-max":
            min_val = original_column.min()
            max_val = original_column.max()
            if max_val == min_val:
                df[column] = 0.5
            else:
                df[column] = (original_column - min_val) / (max_val - min_val)
            operation_desc = f"已对列 '{column}' 进行Min-Max归一化 (0-1)"
        elif method == "z-score":
            mean = original_column.mean()
            std = original_column.std()
            if std == 0:
                df[column] = 0
            else:
                df[column] = (original_column - mean) / std
            operation_desc = f"已对列 '{column}' 进行Z-score标准化"
        elif method == "robust":
            median = original_column.median()
            q1 = original_column.quantile(0.25)
            q3 = original_column.quantile(0.75)
            iqr = q3 - q1
            if iqr == 0:
                df[column] = 0
            else:
                df[column] = (original_column - median) / iqr
            operation_desc = f"已对列 '{column}' 进行稳健归一化"
        else:
            raise ValueError(f"不支持的归一化方法: {method}")
        df[f"{column}_原始"] = original_column
        return df, operation_desc

    def _apply_multi_filter_expr(self, expr):
        df = self.current_df.copy() if self.current_df is not None else None
        if df is None:
            raise ValueError("当前没有加载数据")
        try:
            # 预处理数据，将可能导致问题的字符串列转换为对象类型
            for col in df.columns:
                if df[col].dtype == 'object':
                    df[col] = df[col].astype('string').fillna('')
                
            # 使用pandas query方法进行筛选
            filtered = df.query(expr, engine='python')
            return filtered, f"已按表达式筛选: {expr}"
        except Exception as e:
            # 尝试使用更健壮的布尔索引方式
            try:
                # 替换常见的比较运算符
                expr_replaced = expr.replace('==', ' == ').replace('!=', ' != ').replace('>=', ' >= ').replace('<=', ' <= ')
                expr_replaced = expr_replaced.replace('>', ' > ').replace('<', ' < ')
                
                # 替换 & 和 | 运算符为 and 和 or
                expr_replaced = expr_replaced.replace('&', ' and ').replace('|', ' or ')
                
                # 生成布尔掩码
                mask = eval(expr_replaced, {"__builtins__": {}}, {col: df[col] for col in df.columns})
                
                # 处理掩码中的NaN值
                mask = mask.fillna(False)
                
                # 应用掩码
                filtered = df[mask]
                return filtered, f"已按表达式筛选: {expr}"
            except Exception as e2:
                raise ValueError(f"表达式筛选失败: {str(e)}\n尝试替代方法也失败: {str(e2)}\n\n请检查表达式语法，特别是处理缺失值的部分。")

    def _drop_duplicates(self, cols, keep="first"):
        df = self.current_df.copy() if self.current_df is not None else None
        if df is None:
            raise ValueError("当前没有加载数据")
        # 只允许'first'或'last'，否则默认'first'
        if keep not in ("first", "last"):
            keep = "first"
        if cols.strip():
            col_list = [c.strip() for c in cols.split(',') if c.strip()]
            df2 = df.drop_duplicates(subset=col_list, keep=keep)
            return df2, f"已按列 {col_list} 删除重复行，保留方式: {keep}"
        else:
            df2 = df.drop_duplicates(keep=keep)
            return df2, f"已删除所有重复行，保留方式: {keep}"

    def _drop_blank_rows(self, cols):
        df = self.current_df.copy() if self.current_df is not None else None
        if df is None:
            raise ValueError("当前没有加载数据")
        if cols.strip():
            col_list = [c.strip() for c in cols.split(',') if c.strip()]
            df2 = df.dropna(subset=col_list, how='all')
            return df2, f"已删除指定列({col_list})全为空的行"
        else:
            df2 = df.dropna(how='all')
            return df2, "已删除全为空的行"

    def _apply_operator(self, df, column, operator, value):
        if operator == "==":
            return df[column] == value
        elif operator == ">":
            return df[column] > value
        elif operator == "<":
            return df[column] < value
        elif operator == ">=":
            return df[column] >= value
        elif operator == "<=":
            return df[column] <= value
        elif operator == "!=":
            return df[column] != value
        elif operator == "包含":
            return df[column].astype(str).str.contains(str(value), na=False)
        elif operator == "开始于":
            return df[column].astype(str).str.startswith(str(value), na=False)
        elif operator == "结束于":
            return df[column].astype(str).str.endswith(str(value), na=False)
        else:
            raise ValueError(f"不支持的操作符: {operator}")

    def setup_param_ui(self, op_type, param_frame, widgets):
        """设置参数控件UI"""
        cols = list(self.current_df.columns) if self.current_df is not None else []
        numeric_cols = self.current_df.select_dtypes(include=[np.number]).columns.tolist() if self.current_df is not None else []
        row = 0
        
        # 设置参数框架可拉伸
        param_frame.columnconfigure(1, weight=1)
        param_frame.columnconfigure(3, weight=1)
        
        if op_type in ("delete_rows", "replace_values"):
            ttk.Label(param_frame, text="列:").grid(row=row, column=0, sticky="w")
            col_var = tk.StringVar(value=cols[0] if cols else "")
            col_cb = ttk.Combobox(param_frame, textvariable=col_var, values=cols, width=25)
            col_cb.grid(row=row, column=1, sticky="we", padx=5)
            ttk.Label(param_frame, text="操作符:").grid(row=row, column=2, sticky="w")
            op2_var = tk.StringVar(value="==")
            op2_cb = ttk.Combobox(param_frame, textvariable=op2_var, values=["==", ">", "<", ">=", "<=", "!=", "包含", "开始于", "结束于"], width=12)
            op2_cb.grid(row=row, column=3, sticky="w")
            row += 1
            ttk.Label(param_frame, text="值:").grid(row=row, column=0, sticky="w")
            val_var = tk.StringVar()
            val_entry = ttk.Entry(param_frame, textvariable=val_var, width=30)
            val_entry.grid(row=row, column=1, sticky="we", padx=5)
            widgets.update(col=col_var, op=op2_var, val=val_var)
            if op_type == "replace_values":
                ttk.Label(param_frame, text="新值:").grid(row=row, column=2, sticky="w")
                new_val_var = tk.StringVar()
                new_val_entry = ttk.Entry(param_frame, textvariable=new_val_var, width=30)
                new_val_entry.grid(row=row, column=3, sticky="we", padx=5)
                widgets['new_val'] = new_val_var
        elif op_type == "convert_type":
            ttk.Label(param_frame, text="列:").grid(row=row, column=0, sticky="w")
            col_var = tk.StringVar(value=cols[0] if cols else "")
            col_cb = ttk.Combobox(param_frame, textvariable=col_var, values=cols, width=25)
            col_cb.grid(row=row, column=1, sticky="we", padx=5)
            ttk.Label(param_frame, text="目标类型:").grid(row=row, column=2, sticky="w")
            type_var = tk.StringVar(value="str")
            type_cb = ttk.Combobox(param_frame, textvariable=type_var, values=["int", "float", "str", "datetime", "category", "bool"], width=15)
            type_cb.grid(row=row, column=3, sticky="w")
            widgets.update(col=col_var, type=type_var)
        elif op_type == "handle_missing":
            ttk.Label(param_frame, text="列:").grid(row=row, column=0, sticky="w")
            col_var = tk.StringVar(value=cols[0] if cols else "")
            col_cb = ttk.Combobox(param_frame, textvariable=col_var, values=cols, width=25)
            col_cb.grid(row=row, column=1, sticky="we", padx=5)
            ttk.Label(param_frame, text="处理方式:").grid(row=row, column=2, sticky="w")
            method_var = tk.StringVar(value="填充")
            method_cb = ttk.Combobox(param_frame, textvariable=method_var, values=["填充", "删除", "插值"], width=15)
            method_cb.grid(row=row, column=3, sticky="w")
            row += 1
            ttk.Label(param_frame, text="填充值(如0):").grid(row=row, column=0, sticky="w")
            fill_var = tk.StringVar()
            fill_entry = ttk.Entry(param_frame, textvariable=fill_var, width=30)
            fill_entry.grid(row=row, column=1, sticky="we", padx=5)
            widgets.update(col=col_var, method=method_var, fill=fill_var)
        elif op_type == "create_column":
            ttk.Label(param_frame, text="表达式类型:").grid(row=row, column=0, sticky="w")
            expr_type_var = tk.StringVar(value="计算")
            expr_type_cb = ttk.Combobox(param_frame, textvariable=expr_type_var, values=["计算", "条件", "连接"], width=15)
            expr_type_cb.grid(row=row, column=1, sticky="w")
            row += 1
            ttk.Label(param_frame, text="表达式:").grid(row=row, column=0, sticky="w")
            expr_var = tk.StringVar()
            expr_entry = ttk.Entry(param_frame, textvariable=expr_var, width=60)
            expr_entry.grid(row=row, column=1, columnspan=3, sticky="we", padx=5)
            row += 1
            ttk.Label(param_frame, text="新列名:").grid(row=row, column=0, sticky="w")
            new_col_var = tk.StringVar()
            new_col_entry = ttk.Entry(param_frame, textvariable=new_col_var, width=30)
            new_col_entry.grid(row=row, column=1, sticky="we", padx=5)
            widgets.update(expr_type=expr_type_var, expr=expr_var, new_col=new_col_var)
            # 表达式填写说明
            tip = ("表达式填写示例：\n"
                   "- 计算: 列1 + 列2 * 2\n"
                   "- 条件: 列1 > 10\n"
                   "- 连接: 列1 + '-' + 列2\n"
                   "可用运算符: +, -, *, /，可用numpy/pandas函数，如round(), abs()，列名直接写，无需引号")
            row += 1
            ttk.Label(param_frame, text=tip, foreground="gray").grid(row=row, column=0, columnspan=4, sticky="w")
        elif op_type == "pivot_table":
            # 创建一个内部框架用于透视表特定控件，拉伸以填充更多空间
            pivot_inner_frame = ttk.Frame(param_frame)
            pivot_inner_frame.grid(row=row, column=0, columnspan=6, sticky="nswe", padx=5, pady=5)
            param_frame.columnconfigure(0, weight=1)  # 让列可以拉伸
            
            # 行标签区域
            row_frame = ttk.LabelFrame(pivot_inner_frame, text="行标签区域(必填)")
            row_frame.pack(fill="x", padx=5, pady=5)
            ttk.Label(row_frame, text="行标签:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
            index_var = tk.StringVar()
            index_entry = ttk.Combobox(row_frame, textvariable=index_var, values=cols, width=30)  # 增大宽度
            index_entry.grid(row=0, column=1, sticky="w", padx=5, pady=5)
            ttk.Label(row_frame, text="多个列用逗号分隔").grid(row=0, column=2, sticky="w", padx=5, pady=5)
            
            # 列标签区域
            col_frame = ttk.LabelFrame(pivot_inner_frame, text="列标签区域(可选)")
            col_frame.pack(fill="x", padx=5, pady=5)
            ttk.Label(col_frame, text="列标签:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
            col_var = tk.StringVar()
            col_entry = ttk.Combobox(col_frame, textvariable=col_var, values=cols, width=30)  # 增大宽度
            col_entry.grid(row=0, column=1, sticky="w", padx=5, pady=5)
            ttk.Label(col_frame, text="多个列用逗号分隔，可空").grid(row=0, column=2, sticky="w", padx=5, pady=5)
            
            # 值区域
            val_frame = ttk.LabelFrame(pivot_inner_frame, text="值区域")
            val_frame.pack(fill="x", padx=5, pady=5)
            ttk.Label(val_frame, text="数值列:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
            val_var = tk.StringVar()
            val_entry = ttk.Combobox(val_frame, textvariable=val_var, values=cols, width=30)  # 增大宽度
            val_entry.grid(row=0, column=1, sticky="w", padx=5, pady=5)
            ttk.Label(val_frame, text="多个列用逗号分隔，可空").grid(row=0, column=2, sticky="w", padx=5, pady=5)
            
            ttk.Label(val_frame, text="聚合函数:").grid(row=1, column=0, sticky="w", padx=5, pady=5)
            agg_var = tk.StringVar(value="count")
            agg_cb = ttk.Combobox(val_frame, textvariable=agg_var, values=["count", "sum", "mean", "min", "max", "first", "last", "nunique"], width=15)  # 增大宽度
            agg_cb.grid(row=1, column=1, sticky="w", padx=5, pady=5)
            
            # 筛选区域
            filter_frame = ttk.LabelFrame(pivot_inner_frame, text="筛选区域(可选)")
            filter_frame.pack(fill="x", padx=5, pady=5)
            ttk.Label(filter_frame, text="筛选:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
            filter_var = tk.StringVar()
            filter_entry = ttk.Entry(filter_frame, textvariable=filter_var, width=60)  # 增大宽度
            filter_entry.grid(row=0, column=1, columnspan=2, sticky="we", padx=5, pady=5)
            filter_frame.columnconfigure(1, weight=1)  # 让筛选条件输入框可以拉伸
            
            widgets.update(filter=filter_var, index=index_var, columns=col_var, values=val_var, agg=agg_var)
            
            # 透视表使用说明
            tip_frame = ttk.LabelFrame(pivot_inner_frame, text="使用说明")
            tip_frame.pack(fill="x", padx=5, pady=5)
            tip = ("透视表使用说明:\n"
                   "1. 行标签：必填，指定行方向分组依据，可多选\n"
                   "2. 列标签：可选，指定列方向分组依据，可留空\n"
                   "3. 数值列：计算的数据列，计数时可留空\n"
                   "4. 聚合函数：如何汇总(计数/求和/平均等)\n"
                   "5. 筛选: 如 性别=='男' and 年龄>18")
            tip_label = ttk.Label(tip_frame, text=tip, foreground="gray")
            tip_label.pack(fill="x", padx=5, pady=5)
        elif op_type == "normalize_data":
            ttk.Label(param_frame, text="数值列:").grid(row=row, column=0, sticky="w")
            col_var = tk.StringVar(value=numeric_cols[0] if numeric_cols else (cols[0] if cols else ""))
            col_cb = ttk.Combobox(param_frame, textvariable=col_var, values=numeric_cols if numeric_cols else cols, width=25)
            col_cb.grid(row=row, column=1, sticky="we", padx=5)
            ttk.Label(param_frame, text="方法:").grid(row=row, column=2, sticky="w")
            method_var = tk.StringVar(value="min-max")
            method_cb = ttk.Combobox(param_frame, textvariable=method_var, values=["min-max", "z-score", "robust"], width=15)
            method_cb.grid(row=row, column=3, sticky="w")
            widgets.update(col=col_var, method=method_var)
        elif op_type == "multi_filter":
            ttk.Label(param_frame, text="筛选表达式:").grid(row=row, column=0, sticky="w")
            expr_var = tk.StringVar()
            expr_entry = ttk.Entry(param_frame, textvariable=expr_var, width=70)
            expr_entry.grid(row=row, column=1, columnspan=3, sticky="we", padx=5)
            widgets['expr'] = expr_var
            row += 1
            tip = ("表达式示例：\n"
                   "- (列1 > 10) & (列2 == '男')\n"
                   "- (分数 >= 60) | (班级 == 'A')\n"
                   "- 包含: 列名.str.contains('内容')\n"
                   "- 不包含: ~列名.str.contains('内容')\n"
                   "- 以...开头: 列名.str.startswith('内容')\n"
                   "- 以...结尾: 列名.str.endswith('内容')\n"
                   "- 空值: 列名.isnull()\n"
                   "- 非空: 列名.notnull()\n"
                   "- 多条件组合: (列1 > 10) & (列2 == '男')\n"
                   "- 所有表达式为pandas query语法，字符串需加引号")
            ttk.Label(param_frame, text=tip, foreground="gray").grid(row=row, column=0, columnspan=4, sticky="w")
        elif op_type == "drop_duplicates":
            ttk.Label(param_frame, text="按哪些列去重(可多选,逗号分隔):").grid(row=row, column=0, sticky="w")
            col_var = tk.StringVar()
            col_entry = ttk.Entry(param_frame, textvariable=col_var, width=50)
            col_entry.grid(row=row, column=1, columnspan=3, sticky="we", padx=5)
            widgets['cols'] = col_var
            row += 1
            # 新增保留方式选项
            ttk.Label(param_frame, text="保留方式:").grid(row=row, column=0, sticky="w")
            keep_var = tk.StringVar(value="first")
            keep_cb = ttk.Combobox(param_frame, textvariable=keep_var, values=["first", "last"], width=10)
            keep_cb.grid(row=row, column=1, sticky="w")
            widgets['keep'] = keep_var
            row += 1
            tip = "留空表示全行去重，填写列名用逗号分隔，如：姓名,班级。保留方式：first=保留第一条，last=保留最后一条。"
            ttk.Label(param_frame, text=tip, foreground="gray").grid(row=row, column=0, columnspan=4, sticky="w")
        elif op_type == "drop_blank_rows":
            ttk.Label(param_frame, text="按哪些列判定空白(可多选,逗号分隔):").grid(row=row, column=0, sticky="w")
            col_var = tk.StringVar()
            col_entry = ttk.Entry(param_frame, textvariable=col_var, width=50)
            col_entry.grid(row=row, column=1, columnspan=3, sticky="we", padx=5)
            widgets['cols'] = col_var
            row += 1
            tip = "留空表示全行空白才删除，填写列名用逗号分隔，如：成绩,备注"
            ttk.Label(param_frame, text=tip, foreground="gray").grid(row=row, column=0, columnspan=4, sticky="w")
        elif op_type == "ai_categorize":
            ttk.Label(param_frame, text="数据列:").grid(row=row, column=0, sticky="w")
            col_var = tk.StringVar(value=cols[0] if cols else "")
            col_cb = ttk.Combobox(param_frame, textvariable=col_var, values=cols, width=25)
            col_cb.grid(row=row, column=1, sticky="we", padx=5)

            row += 1
            # 预设配置选择
            ttk.Label(param_frame, text="预设配置:").grid(row=row, column=0, sticky="w")

            # 加载配置文件
            config_data = self._load_categorize_config()
            config_names = list(config_data.get("分类配置", {}).keys())
            config_names.insert(0, "自定义配置")

            config_var = tk.StringVar(value="自定义配置")
            config_cb = ttk.Combobox(param_frame, textvariable=config_var, values=config_names, width=25, state="readonly")
            config_cb.grid(row=row, column=1, sticky="we", padx=5)

            # 配置说明标签
            config_desc_var = tk.StringVar(value="请选择预设配置或使用自定义配置")
            config_desc_label = ttk.Label(param_frame, textvariable=config_desc_var, foreground="blue", wraplength=300)
            config_desc_label.grid(row=row, column=2, columnspan=1, sticky="w", padx=5)

            # 配置管理按钮
            config_btn = ttk.Button(param_frame, text="管理配置",
                                   command=lambda: self._open_config_manager(config_cb, config_data))
            config_btn.grid(row=row, column=3, sticky="w", padx=5)

            row += 1
            ttk.Label(param_frame, text="分类标准:").grid(row=row, column=0, sticky="w", padx=5, pady=5)

            # 使用文本区域来允许多行输入
            category_frame = ttk.Frame(param_frame)
            category_frame.grid(row=row, column=1, columnspan=3, sticky="we", padx=5, pady=5)

            category_text = scrolledtext.ScrolledText(category_frame, wrap=tk.WORD, height=6, width=50)
            category_text.pack(fill="both", expand=True)
            category_text.insert("1.0", "请详细描述您希望AI如何对数据进行分类的标准和规则：\n例如：\n将产品评论按情感分为：正面、中性、负面\n您的分类标准越具体，AI分类结果越准确。")

            # 使用自定义的StringVar和trace来同步文本区域和变量
            category_var = tk.StringVar()

            def update_var(*args):
                category_var.set(category_text.get("1.0", "end-1c"))

            # 绑定文本修改事件
            category_text.bind("<KeyRelease>", update_var)

            # 配置选择变化时更新分类标准
            def on_config_change(*args):
                selected_config = config_var.get()
                if selected_config != "自定义配置" and selected_config in config_data.get("分类配置", {}):
                    preset = config_data["分类配置"][selected_config]
                    # 更新描述
                    config_desc_var.set(preset.get("描述", ""))
                    # 更新分类标准
                    category_text.delete("1.0", "end")
                    category_text.insert("1.0", preset.get("分类标准", ""))
                    category_var.set(preset.get("分类标准", ""))

                    # 显示类别和示例信息
                    categories = preset.get("类别", [])
                    examples = preset.get("示例", {})
                    if categories:
                        info_text = f"\n\n预设类别: {', '.join(categories)}"
                        if examples:
                            info_text += "\n\n示例："
                            for cat, ex_list in examples.items():
                                info_text += f"\n- {cat}: {', '.join(ex_list[:3])}等"
                        category_text.insert("end", info_text)
                        category_var.set(category_text.get("1.0", "end-1c"))
                else:
                    config_desc_var.set("请选择预设配置或使用自定义配置")
                    if selected_config == "自定义配置":
                        category_text.delete("1.0", "end")
                        category_text.insert("1.0", "请详细描述您希望AI如何对数据进行分类的标准和规则：\n例如：\n将产品评论按情感分为：正面、中性、负面\n您的分类标准越具体，AI分类结果越准确。")
                        category_var.set(category_text.get("1.0", "end-1c"))

            config_var.trace_add('write', on_config_change)

            row += 1
            ttk.Label(param_frame, text="新列名:").grid(row=row, column=0, sticky="w")
            new_col_var = tk.StringVar(value="AI分类结果")
            new_col_entry = ttk.Entry(param_frame, textvariable=new_col_var, width=25)
            new_col_entry.grid(row=row, column=1, sticky="we", padx=5)

            row += 1
            ttk.Label(param_frame, text="批处理大小:").grid(row=row, column=0, sticky="w")
            batch_var = tk.StringVar(value="3")
            batch_entry = ttk.Entry(param_frame, textvariable=batch_var, width=10)
            batch_entry.grid(row=row, column=1, sticky="w", padx=5)
            ttk.Label(param_frame, text="(推荐1-5，越小越精确)", foreground="gray").grid(row=row, column=2, sticky="w")

            row += 1
            tip = ("使用说明:\n"
                   "1. 选择要分析的列\n"
                   "2. 选择预设配置或自定义分类标准\n"
                   "3. 指定结果保存的新列名\n"
                   "4. AI会根据配置进行智能分类\n"
                   "5. 批处理大小建议1-5，平衡精确度和速度")
            ttk.Label(param_frame, text=tip, foreground="gray").grid(row=row, column=0, columnspan=4, sticky="w", padx=5, pady=5)

            # 存储控件变量
            widgets.update(col=col_var, category_prompt=category_var, new_col=new_col_var, batch_size=batch_var, config_name=config_var)
        elif op_type == "similarity_analysis":
            # 第一列选择
            ttk.Label(param_frame, text="第一列:").grid(row=row, column=0, sticky="w")
            col1_var = tk.StringVar(value=cols[0] if cols else "")
            col1_cb = ttk.Combobox(param_frame, textvariable=col1_var, values=cols, width=25)
            col1_cb.grid(row=row, column=1, sticky="we", padx=5)
            
            # 第二列选择
            ttk.Label(param_frame, text="第二列:").grid(row=row, column=2, sticky="w")
            col2_var = tk.StringVar(value=cols[1] if len(cols) > 1 else (cols[0] if cols else ""))
            col2_cb = ttk.Combobox(param_frame, textvariable=col2_var, values=cols, width=25)
            col2_cb.grid(row=row, column=3, sticky="we", padx=5)
            
            row += 1
            # 相似度阈值
            ttk.Label(param_frame, text="相似度阈值(0-1):").grid(row=row, column=0, sticky="w")
            threshold_var = tk.StringVar(value="0.8")
            threshold_entry = ttk.Entry(param_frame, textvariable=threshold_var, width=10)
            threshold_entry.grid(row=row, column=1, sticky="w", padx=5)
            
            # 结果列名
            ttk.Label(param_frame, text="结果列名:").grid(row=row, column=2, sticky="w")
            result_col_var = tk.StringVar(value="相似度")
            result_col_entry = ttk.Entry(param_frame, textvariable=result_col_var, width=25)
            result_col_entry.grid(row=row, column=3, sticky="we", padx=5)
            
            row += 1
            # 添加重排选项
            reorder_var = tk.BooleanVar(value=False)
            reorder_cb = ttk.Checkbutton(param_frame, text="启用内容重排（将第二列与第一列相似度最高的内容放在同一行）", 
                                      variable=reorder_var)
            reorder_cb.grid(row=row, column=0, columnspan=4, sticky="w", padx=5, pady=5)
            
            row += 1
            tip = ("使用说明:\n"
                   "1. 选择要比较相似度的两列\n"
                   "2. 设置相似度阈值(0-1之间)，1表示完全相同，0表示完全不同\n"
                   "3. 指定结果保存的列名\n"
                   "4. 如启用内容重排，将自动为每一行的第一列内容寻找最相似的第二列内容，并将其放在同一行\n"
                   "5. 分析结果将包含每行两列数据的相似度值和是否超过阈值的标记")
            ttk.Label(param_frame, text=tip, foreground="gray").grid(row=row, column=0, columnspan=4, sticky="w", padx=5, pady=5)
            
            # 存储控件变量
            widgets.update(col1=col1_var, col2=col2_var, threshold=threshold_var, new_col=result_col_var, reorder=reorder_var)

    def execute_special_op(self, op_type, widgets):
        """执行特殊操作并返回处理后的数据框和描述"""
        if op_type == "delete_rows":
            return self._delete_rows(widgets['col'].get(), widgets['op'].get(), widgets['val'].get())
        elif op_type == "replace_values":
            return self._replace_values(widgets['col'].get(), widgets['op'].get(), widgets['val'].get(), widgets['new_val'].get())
        elif op_type == "convert_type":
            return self._convert_column_type(widgets['col'].get(), widgets['type'].get())
        elif op_type == "handle_missing":
            return self._handle_missing_values(widgets['col'].get(), widgets['method'].get(), widgets['fill'].get())
        elif op_type == "create_column":
            return self._create_new_column(None, widgets['expr_type'].get(), widgets['expr'].get(), widgets['new_col'].get())
        elif op_type == "pivot_table":
            return self._create_pivot_table(
                widgets['index'].get(),
                widgets['columns'].get(),
                widgets['values'].get(), 
                widgets['agg'].get(),
                widgets['filter'].get()
            )
        elif op_type == "normalize_data":
            return self._normalize_data(widgets['col'].get(), widgets['method'].get())
        elif op_type == "multi_filter":
            expr = widgets['expr'].get()
            if not expr:
                raise ValueError("请填写筛选表达式")
            return self._apply_multi_filter_expr(expr)
        elif op_type == "drop_duplicates":
            return self._drop_duplicates(widgets['cols'].get(), widgets['keep'].get())
        elif op_type == "drop_blank_rows":
            return self._drop_blank_rows(widgets['cols'].get())
        elif op_type == "ai_categorize":
            try:
                batch_size = int(widgets['batch_size'].get())
                if batch_size < 1:
                    batch_size = 3
            except (ValueError, TypeError):
                batch_size = 3

            # 获取配置名称
            config_name = widgets.get('config_name', tk.StringVar()).get()
            if config_name == "自定义配置":
                config_name = None

            return self._ai_categorize_column(
                widgets['col'].get(),
                widgets['category_prompt'].get(),
                widgets['new_col'].get(),
                batch_size,
                config_name
            )
        elif op_type == "similarity_analysis":
            return self._similarity_analysis(
                widgets['col1'].get(),
                widgets['col2'].get(),
                widgets['threshold'].get(),
                widgets['new_col'].get(),
                widgets['reorder'].get()
            )
        else:
            raise ValueError(f"不支持的操作类型: {op_type}")

    def create_bar_chart(self, column_name, title=''):
        """创建柱状图并显示"""
        if self.current_df is None:
            self.update_chat("请先加载数据文件")
            return
        
        # 清除之前的图表
        if self.viz_frame:
            self.viz_frame.pack_forget()
        
        # 重新创建可视化区域
        self.viz_frame = ttk.LabelFrame(self.root, text="数据可视化")
        self.viz_frame.pack(padx=10, pady=5, fill="both", expand=True)
        
        # 创建figure对象
        self.current_figure = plt.figure(figsize=(10, 6))
        ax = self.current_figure.add_subplot(111)
        
        # 绘制柱状图
        counts = self.current_df[column_name].value_counts()
        counts.plot(kind='bar', ax=ax, title=title)
        
        # 设置标签
        ax.set_xlabel(column_name)
        ax.set_ylabel('计数')
        
        # 添加数据标签 - 确保标签在柱子内部
        for i, v in enumerate(counts):
            ax.text(i, v/2, str(v), ha='center', va='center', fontweight='bold')
        
        # 调整布局
        plt.tight_layout()
        
        # 创建canvas显示图表
        self.canvas = FigureCanvasTkAgg(self.current_figure, master=self.viz_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill="both", expand=True)
        
        # 添加图表操作区域
        btn_frame = ttk.Frame(self.viz_frame)
        btn_frame.pack(fill="x", padx=5, pady=5)
        
        # 添加保存按钮
        save_btn = ttk.Button(btn_frame, text="保存图表", 
                             command=lambda: self.save_current_figure())
        save_btn.pack(side="left", padx=5)
        
        # 添加复制到剪贴板按钮（如果支持）
        if sys.platform == 'win32' and 'win32clipboard' in sys.modules:
            copy_btn = ttk.Button(btn_frame, text="复制到剪贴板", 
                                 command=lambda: self.copy_figure_to_clipboard())
            copy_btn.pack(side="left", padx=5)
        
        self.update_chat(f"已创建'{column_name}'的柱状图")
        return self.current_figure
        
    def save_current_figure(self):
        """保存当前图表到文件"""
        if self.current_figure is None:
            self.update_chat("没有可保存的图表")
            return
            
        file_path = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[("PNG图片", "*.png"), ("JPG图片", "*.jpg"), 
                      ("PDF文档", "*.pdf"), ("SVG矢量图", "*.svg")]
        )
        if not file_path:
            return
            
        try:
            self.current_figure.savefig(file_path, dpi=300, bbox_inches='tight')
            self.update_chat(f"图表已保存到: {file_path}")
        except Exception as e:
            self.update_chat(f"保存图表失败: {str(e)}")
            
    def copy_figure_to_clipboard(self):
        """复制图表到剪贴板（仅Windows平台）"""
        if self.current_figure is None or sys.platform != 'win32':
            return
            
        if 'win32clipboard' not in sys.modules or 'PIL' not in sys.modules:
            self.update_chat("复制到剪贴板需要安装pywin32和PIL库")
            return
            
        try:
            # 导入需要的模块
            import win32clipboard
            from PIL import Image
            
            # 保存到临时内存文件
            buf = io.BytesIO()
            self.current_figure.savefig(buf, format='png', dpi=150)
            buf.seek(0)
            
            # 打开为PIL图像并复制到剪贴板
            image = Image.open(buf)
            output = io.BytesIO()
            image.convert('RGB').save(output, 'BMP')
            data = output.getvalue()[14:]  # 剔除BMP文件头
            output.close()
            
            win32clipboard.OpenClipboard()
            win32clipboard.EmptyClipboard()
            win32clipboard.SetClipboardData(win32clipboard.CF_DIB, data)
            win32clipboard.CloseClipboard()
            
            self.update_chat("图表已复制到剪贴板")
        except Exception as e:
            self.update_chat(f"复制到剪贴板失败: {str(e)}")

    def _load_categorize_config(self):
        """加载分类配置文件"""
        config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "ai_categorize_config.json")
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # 如果配置文件不存在，返回空配置
                return {"分类配置": {}}
        except Exception as e:
            self.update_chat(f"加载分类配置文件失败: {str(e)}")
            return {"分类配置": {}}

    def _load_knowledge_base(self):
        """加载专业知识库"""
        knowledge_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "专业知识库.json")
        try:
            if os.path.exists(knowledge_file):
                with open(knowledge_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return {}
        except Exception as e:
            self.update_chat(f"加载专业知识库失败: {str(e)}")
            return {}

    def _rule_based_pre_classification(self, text, knowledge_base):
        """基于规则的预分类"""
        if not knowledge_base or not text:
            return None, 0.0

        rules = knowledge_base.get("分类规则库", {}).get("关键词映射", {})
        text_lower = text.lower()

        # 计算每个类别的匹配分数
        category_scores = {}
        for category, keywords in rules.items():
            score = 0
            matched_keywords = []
            for keyword in keywords:
                if keyword.lower() in text_lower:
                    score += 1
                    matched_keywords.append(keyword)

            if score > 0:
                # 考虑关键词长度和匹配数量
                category_scores[category] = {
                    'score': score,
                    'keywords': matched_keywords,
                    'confidence': min(score * 0.2, 0.8)  # 最高置信度0.8
                }

        if category_scores:
            # 选择得分最高的类别
            best_category = max(category_scores.keys(), key=lambda x: category_scores[x]['score'])
            return best_category, category_scores[best_category]['confidence']

        return None, 0.0

    def _enhance_text_with_knowledge(self, text, knowledge_base):
        """使用知识库增强文本理解"""
        if not knowledge_base or not text:
            return text

        enhanced_text = text

        # 识别元器件
        components = knowledge_base.get("元器件知识库", {})
        identified_components = []
        for category, items in components.items():
            if isinstance(items, dict):
                for subcategory, component_list in items.items():
                    for component in component_list:
                        if component in text:
                            identified_components.append(f"{component}({subcategory})")
            elif isinstance(items, list):
                for component in items:
                    if component in text:
                        identified_components.append(f"{component}({category})")

        if identified_components:
            enhanced_text += f"\n[识别的元器件: {', '.join(identified_components)}]"

        # 识别工艺术语
        processes = knowledge_base.get("工艺术语库", {})
        identified_processes = []
        for category, items in processes.items():
            if isinstance(items, dict):
                for subcategory, process_list in items.items():
                    for process in process_list:
                        if process in text:
                            identified_processes.append(f"{process}({subcategory})")

        if identified_processes:
            enhanced_text += f"\n[识别的工艺: {', '.join(identified_processes)}]"

        return enhanced_text

    def _calculate_confidence_score(self, text, predicted_category, knowledge_base, categories):
        """计算分类结果的置信度分数"""
        if not text or not predicted_category:
            return 0.0

        confidence = 0.0

        # 1. 规则匹配置信度
        rules = knowledge_base.get("分类规则库", {}).get("关键词映射", {})
        if predicted_category in rules:
            keywords = rules[predicted_category]
            matched_keywords = sum(1 for keyword in keywords if keyword.lower() in text.lower())
            if matched_keywords > 0:
                confidence += min(matched_keywords * 0.2, 0.4)

        # 2. 专业术语识别置信度
        components = knowledge_base.get("元器件知识库", {})
        processes = knowledge_base.get("工艺术语库", {})

        # 检查是否包含相关专业术语
        has_components = any(comp in text for category in components.values()
                           for subcategory in (category.values() if isinstance(category, dict) else [category])
                           for comp in (subcategory if isinstance(subcategory, list) else []))

        has_processes = any(proc in text for category in processes.values()
                          for subcategory in (category.values() if isinstance(category, dict) else [category])
                          for proc in (subcategory if isinstance(subcategory, list) else []))

        if has_components or has_processes:
            confidence += 0.2

        # 3. 类别有效性检查
        if categories and predicted_category in categories:
            confidence += 0.2
        elif predicted_category.endswith('[规则]'):
            confidence += 0.3  # 规则引擎预测的额外置信度

        # 4. 文本长度和详细程度
        if len(text) > 20:  # 较详细的描述
            confidence += 0.1
        if len(text) > 50:  # 非常详细的描述
            confidence += 0.1

        return min(confidence, 1.0)

    def _validate_classification_result(self, text, result, knowledge_base, categories):
        """验证分类结果的合理性"""
        if not result or result in ["未知", "分类错误", "其他"]:
            return result, 0.0

        # 计算置信度
        confidence = self._calculate_confidence_score(text, result, knowledge_base, categories)

        # 清理结果标记
        clean_result = result.replace('[规则]', '')

        # 低置信度标记
        if confidence < 0.3:
            return f"{clean_result}[低置信度]", confidence
        elif confidence < 0.5:
            return f"{clean_result}[中置信度]", confidence
        else:
            return clean_result, confidence

    def _generate_enhanced_prompt(self, category_prompt, sample_data, categories=None, examples=None, config_name=None):
        """生成增强的分类提示词"""
        # 检查是否为问题原因分类，使用专门的提示词
        if config_name == "问题原因分类":
            knowledge_base = self._load_knowledge_base()
            return self._generate_problem_cause_prompt(category_prompt, sample_data, categories, examples, knowledge_base)

        # 基础提示词
        base_prompt = f"""你是一个专业的数据分类专家。请根据以下分类标准对数据进行精确分类：

分类标准：
{category_prompt}

"""

        # 如果有预定义类别，添加类别说明
        if categories:
            base_prompt += f"可选类别：{', '.join(categories)}\n\n"

        # 如果有示例，添加示例说明
        if examples:
            base_prompt += "分类示例：\n"
            for category, example_list in examples.items():
                base_prompt += f"- {category}：{', '.join(example_list[:3])}等\n"
            base_prompt += "\n"

        # 添加数据样本分析
        if sample_data:
            base_prompt += f"数据样本预览：{sample_data[:5]}\n\n"

        # 添加输出格式要求
        base_prompt += """重要要求：
1. 严格按照给定的分类标准进行分类
2. 每个数据项必须分配到一个明确的类别
3. 输出格式：每行一个分类结果，不要有编号、标点符号或额外说明
4. 如果数据不清楚或无法分类，请输出"未知"
5. 保持与输入数据相同的顺序

"""
        return base_prompt

    def _generate_problem_cause_prompt(self, category_prompt, sample_data, categories=None, examples=None, knowledge_base=None):
        """生成专门用于问题原因分类的提示词"""
        base_prompt = f"""你是一个专业的质量管理专家，专门负责产品质量问题的根因分析和分类。请根据以下分类标准对质量问题进行精确的原因分类：

分类标准：
{category_prompt}

分类层次结构：
【设计阶段】外部需求、需求分析、接口设计、功能性能和物理特性设计、通用质量特性设计、试验验证设计、技术认知
【软件阶段】软件外部需求、软件设计、软件测试、软件管理
【工艺阶段】工艺设计、工艺文件可操作性、工艺不稳定、工艺工装设计、工艺认知
【管理阶段】制度、培训、责任制不落实、人员违反规章制度、人员疏忽大意、人员违规操作
【元器件阶段】元器件准入、元器件设计、元器件工艺、元器件生产管理和操作、元器件偶发失效、元器件固有缺陷且未剔除
【外协外购件阶段】外协外购件准入、外协外购件设计、外协外购件工艺、外协外购件生产管理、外协外购件偶发失效、外协外购件固有缺陷且未剔除
【使用阶段】用户使用不当、外部环境、复试无故障、其他

"""

        # 添加专业知识库信息
        if knowledge_base:
            base_prompt += "专业知识参考：\n"

            # 添加元器件知识
            components = knowledge_base.get("元器件知识库", {})
            if components:
                base_prompt += "常见元器件类型：\n"
                for category, items in components.items():
                    if isinstance(items, dict):
                        for subcategory, component_list in items.items():
                            base_prompt += f"- {subcategory}：{', '.join(component_list[:5])}等\n"
                base_prompt += "\n"

            # 添加工艺术语
            processes = knowledge_base.get("工艺术语库", {})
            if processes:
                base_prompt += "常见工艺术语：\n"
                for category, items in processes.items():
                    if isinstance(items, dict):
                        for subcategory, process_list in items.items():
                            base_prompt += f"- {subcategory}：{', '.join(process_list[:5])}等\n"
                base_prompt += "\n"

            # 添加专业术语解释
            terminology = knowledge_base.get("专业术语解释", {})
            if terminology:
                base_prompt += "专业术语解释：\n"
                for category, terms in terminology.items():
                    if isinstance(terms, dict):
                        for term, explanation in terms.items():
                            base_prompt += f"- {term}：{explanation}\n"
                base_prompt += "\n"

        base_prompt += """分类原则：
1. 分析问题的根本原因，而非表面现象
2. 优先考虑设计阶段问题，其次是工艺、管理等阶段
3. 对于复合原因，选择主要原因进行分类
4. 仔细区分不同阶段的相似问题类型
5. 识别关键词：元器件名称、工艺术语、技术概念等

"""

        # 添加典型示例
        if examples:
            base_prompt += "典型分类示例：\n"
            for category, example_list in examples.items():
                examples_str = ", ".join(example_list[:2])  # 显示前2个示例
                base_prompt += f"• {category}：{examples_str}\n"
            base_prompt += "\n"

        # 添加数据样本预览
        if sample_data:
            base_prompt += f"待分类问题样本：{sample_data[:3]}\n\n"

        # 添加专门的输出要求
        base_prompt += """输出要求：
1. 仔细分析每个问题描述，识别关键信息
2. 判断问题发生的主要阶段和具体原因
3. 严格按照37个预定义类别进行分类
4. 输出格式：每行一个分类结果，只输出类别名称
5. 无法明确分类的问题输出"其他"
6. 保持与输入数据相同的顺序

"""

        return base_prompt

    def _ai_categorize_column(self, column, category_prompt, new_column_name, batch_size=1, config_name=None):
        """使用Ollama大模型对列数据进行分类并创建新列

        Args:
            column: 要分析的列名
            category_prompt: 用于指导AI分类的提示词
            new_column_name: 结果新列的名称
            batch_size: 每次批处理的行数，避免发送过多请求
            config_name: 预设配置名称（可选）
        """
        df = self.current_df.copy() if self.current_df is not None else None
        if df is None:
            raise ValueError("当前没有加载数据")
        if not new_column_name:
            raise ValueError("请指定新列名称")
        if column not in df.columns:
            raise ValueError(f"列 '{column}' 不存在")

        # 检查Ollama服务是否可用
        if not self.ollama.available:
            self.ollama.available = self.ollama._check_availability()
            if not self.ollama.available:
                raise ValueError("无法连接到Ollama服务，请确保Ollama已启动")

        # 加载配置文件
        config_data = self._load_categorize_config()
        categories = None
        examples = None

        # 如果指定了配置名称，使用预设配置
        if config_name and config_name in config_data.get("分类配置", {}):
            preset_config = config_data["分类配置"][config_name]
            if not category_prompt or category_prompt.strip() == "":
                category_prompt = preset_config.get("分类标准", "")
            categories = preset_config.get("类别", [])
            examples = preset_config.get("示例", {})

        # 获取数据样本用于提示词优化
        sample_data = df[column].dropna().head(10).tolist()

        # 生成增强的提示词
        enhanced_prompt = self._generate_enhanced_prompt(
            category_prompt, sample_data, categories, examples, config_name
        )

        # 总行数和进度计算
        total_rows = len(df)
        results = []
        error_count = 0

        # 创建进度对话框
        progress_window = tk.Toplevel(self.root)
        progress_window.title("AI智能分类进度")
        progress_window.geometry("500x200")
        progress_window.resizable(False, False)

        # 进度标签
        progress_label = ttk.Label(progress_window, text="正在进行AI智能分类，请稍候...")
        progress_label.pack(pady=10)

        # 进度条
        progress_bar = ttk.Progressbar(progress_window, orient="horizontal",
                                      length=450, mode="determinate")
        progress_bar.pack(pady=10)
        progress_bar["maximum"] = total_rows

        # 状态标签
        status_label = ttk.Label(progress_window, text=f"已处理: 0/{total_rows}")
        status_label.pack(pady=5)

        # 错误统计标签
        error_label = ttk.Label(progress_window, text="分类错误: 0", foreground="red")
        error_label.pack(pady=5)

        # 更新界面
        progress_window.update()

        try:
            # 分批处理数据
            for i in range(0, total_rows, batch_size):
                batch_end = min(i + batch_size, total_rows)
                batch_data = df.iloc[i:batch_end][column].tolist()

                # 过滤空值
                valid_data = []
                valid_indices = []
                for idx, item in enumerate(batch_data):
                    if pd.notna(item) and str(item).strip():
                        valid_data.append(str(item).strip())
                        valid_indices.append(idx)

                batch_results = ["未知"] * len(batch_data)  # 默认结果

                if valid_data:
                    # 加载知识库用于规则引擎
                    knowledge_base = self._load_knowledge_base()

                    # 对每个数据项进行增强处理
                    enhanced_data = []
                    rule_predictions = []

                    for item in valid_data:
                        # 使用知识库增强文本理解
                        enhanced_item = self._enhance_text_with_knowledge(item, knowledge_base)
                        enhanced_data.append(enhanced_item)

                        # 规则引擎预分类
                        rule_pred, rule_conf = self._rule_based_pre_classification(item, knowledge_base)
                        rule_predictions.append((rule_pred, rule_conf))

                    # 构建完整提示词
                    full_prompt = enhanced_prompt + f"""
待分类数据：
{chr(10).join(f"{i+1}. {item}" for i, item in enumerate(enhanced_data))}

请按顺序返回分类结果："""

                    # 多轮AI分类验证
                    ai_results = []
                    for round_num in range(2):  # 进行2轮分类
                        try:
                            response = self.ollama.ask(full_prompt)
                            lines = [line.strip() for line in response.strip().split('\n') if line.strip()]

                            # 清理结果
                            cleaned_lines = []
                            for line in lines:
                                cleaned_line = re.sub(r'^\d+[\.\)]\s*', '', line)
                                cleaned_line = cleaned_line.strip()
                                if cleaned_line:
                                    cleaned_lines.append(cleaned_line)

                            if len(cleaned_lines) == len(valid_data):
                                ai_results.append(cleaned_lines)
                                break
                        except:
                            continue

                    # 融合规则引擎和AI结果，并进行置信度评估
                    final_results = []
                    confidence_scores = []

                    for i, (rule_pred, rule_conf) in enumerate(rule_predictions):
                        original_text = valid_data[i]

                        if rule_conf > 0.6 and rule_pred:  # 高置信度规则预测
                            candidate_result = f"{rule_pred}[规则]"
                        elif ai_results and i < len(ai_results[0]):
                            ai_pred = ai_results[0][i]
                            # 验证AI结果是否在有效类别中
                            if categories and ai_pred in categories:
                                candidate_result = ai_pred
                            elif rule_pred:  # 回退到规则预测
                                candidate_result = f"{rule_pred}[规则]"
                            else:
                                candidate_result = ai_pred
                        else:
                            candidate_result = "其他"

                        # 验证和评估置信度
                        validated_result, confidence = self._validate_classification_result(
                            original_text, candidate_result, knowledge_base, categories
                        )

                        final_results.append(validated_result)
                        confidence_scores.append(confidence)

                    # 将结果映射回原始位置
                    if len(final_results) == len(valid_data):
                        for idx, result in zip(valid_indices, final_results):
                            batch_results[idx] = result
                    else:
                        error_count += len(valid_data)
                        for idx in valid_indices:
                            batch_results[idx] = "分类错误"

                results.extend(batch_results)

                # 更新进度
                progress_bar["value"] = batch_end
                status_label.configure(text=f"已处理: {batch_end}/{total_rows}")
                error_label.configure(text=f"分类错误: {error_count}")
                progress_window.update()

        except Exception as e:
            progress_window.destroy()
            raise ValueError(f"AI分析过程中出错: {str(e)}")

        # 关闭进度窗口
        progress_window.destroy()

        # 将结果添加到DataFrame
        df[new_column_name] = results

        # 统计分类结果和置信度
        result_counts = df[new_column_name].value_counts()

        # 统计置信度分布
        low_confidence_count = sum(1 for result in results if '[低置信度]' in str(result))
        medium_confidence_count = sum(1 for result in results if '[中置信度]' in str(result))
        rule_based_count = sum(1 for result in results if '[规则]' in str(result))

        stats_info = f"分类统计: {dict(result_counts)}"
        confidence_info = f"置信度分布: 低置信度 {low_confidence_count}个, 中置信度 {medium_confidence_count}个, 规则预测 {rule_based_count}个"

        operation_desc = f"已使用增强AI对列 '{column}' 进行智能分类，结果保存在新列 '{new_column_name}'\n{stats_info}\n{confidence_info}"

        if error_count > 0:
            operation_desc += f"\n注意：有 {error_count} 个数据项分类可能不准确，建议检查"

        if low_confidence_count > 0:
            operation_desc += f"\n建议：有 {low_confidence_count} 个低置信度结果，建议人工复核"

        return df, operation_desc

    def _open_config_manager(self, config_combobox, config_data):
        """打开分类配置管理器"""
        config_win = tk.Toplevel(self.root)
        config_win.title("AI分类配置管理器")
        config_win.geometry("800x600")
        config_win.resizable(True, True)

        # 创建主框架
        main_frame = ttk.Frame(config_win)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # 左侧配置列表
        left_frame = ttk.LabelFrame(main_frame, text="配置列表")
        left_frame.pack(side="left", fill="y", padx=(0, 10))

        # 配置列表
        config_listbox = tk.Listbox(left_frame, width=25, height=20)
        config_listbox.pack(fill="both", expand=True, padx=5, pady=5)

        # 加载配置列表
        configs = list(config_data.get("分类配置", {}).keys())
        for config_name in configs:
            config_listbox.insert(tk.END, config_name)

        # 右侧配置详情
        right_frame = ttk.LabelFrame(main_frame, text="配置详情")
        right_frame.pack(side="right", fill="both", expand=True)

        # 配置名称
        ttk.Label(right_frame, text="配置名称:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        name_var = tk.StringVar()
        name_entry = ttk.Entry(right_frame, textvariable=name_var, width=30)
        name_entry.grid(row=0, column=1, sticky="we", padx=5, pady=5)

        # 配置描述
        ttk.Label(right_frame, text="描述:").grid(row=1, column=0, sticky="nw", padx=5, pady=5)
        desc_text = scrolledtext.ScrolledText(right_frame, height=3, width=50)
        desc_text.grid(row=1, column=1, sticky="we", padx=5, pady=5)

        # 分类标准
        ttk.Label(right_frame, text="分类标准:").grid(row=2, column=0, sticky="nw", padx=5, pady=5)
        standard_text = scrolledtext.ScrolledText(right_frame, height=8, width=50)
        standard_text.grid(row=2, column=1, sticky="we", padx=5, pady=5)

        # 类别列表
        ttk.Label(right_frame, text="类别(逗号分隔):").grid(row=3, column=0, sticky="w", padx=5, pady=5)
        categories_var = tk.StringVar()
        categories_entry = ttk.Entry(right_frame, textvariable=categories_var, width=50)
        categories_entry.grid(row=3, column=1, sticky="we", padx=5, pady=5)

        # 示例
        ttk.Label(right_frame, text="示例(JSON格式):").grid(row=4, column=0, sticky="nw", padx=5, pady=5)
        examples_text = scrolledtext.ScrolledText(right_frame, height=6, width=50)
        examples_text.grid(row=4, column=1, sticky="we", padx=5, pady=5)

        # 按钮框架
        btn_frame = ttk.Frame(right_frame)
        btn_frame.grid(row=5, column=0, columnspan=2, pady=10)

        def load_config():
            """加载选中的配置"""
            selection = config_listbox.curselection()
            if selection:
                config_name = config_listbox.get(selection[0])
                config = config_data["分类配置"][config_name]

                name_var.set(config_name)
                desc_text.delete("1.0", "end")
                desc_text.insert("1.0", config.get("描述", ""))
                standard_text.delete("1.0", "end")
                standard_text.insert("1.0", config.get("分类标准", ""))
                categories_var.set(", ".join(config.get("类别", [])))
                examples_text.delete("1.0", "end")
                examples_text.insert("1.0", json.dumps(config.get("示例", {}), ensure_ascii=False, indent=2))

        def save_config():
            """保存配置"""
            config_name = name_var.get().strip()
            if not config_name:
                messagebox.showerror("错误", "请输入配置名称")
                return

            try:
                # 解析类别
                categories = [cat.strip() for cat in categories_var.get().split(",") if cat.strip()]

                # 解析示例
                examples_str = examples_text.get("1.0", "end-1c").strip()
                examples = {}
                if examples_str:
                    examples = json.loads(examples_str)

                # 创建配置
                new_config = {
                    "描述": desc_text.get("1.0", "end-1c").strip(),
                    "分类标准": standard_text.get("1.0", "end-1c").strip(),
                    "类别": categories,
                    "示例": examples
                }

                # 保存到配置数据
                if "分类配置" not in config_data:
                    config_data["分类配置"] = {}
                config_data["分类配置"][config_name] = new_config

                # 保存到文件
                config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "ai_categorize_config.json")
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, ensure_ascii=False, indent=2)

                # 更新列表
                if config_name not in [config_listbox.get(i) for i in range(config_listbox.size())]:
                    config_listbox.insert(tk.END, config_name)

                # 更新下拉框
                current_values = list(config_combobox['values'])
                if config_name not in current_values:
                    current_values.append(config_name)
                    config_combobox['values'] = current_values

                messagebox.showinfo("成功", f"配置 '{config_name}' 已保存")

            except json.JSONDecodeError:
                messagebox.showerror("错误", "示例JSON格式错误")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")

        def delete_config():
            """删除配置"""
            selection = config_listbox.curselection()
            if not selection:
                messagebox.showwarning("警告", "请选择要删除的配置")
                return

            config_name = config_listbox.get(selection[0])
            if messagebox.askyesno("确认", f"确定要删除配置 '{config_name}' 吗？"):
                try:
                    # 从配置数据中删除
                    del config_data["分类配置"][config_name]

                    # 保存到文件
                    config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "ai_categorize_config.json")
                    with open(config_file, 'w', encoding='utf-8') as f:
                        json.dump(config_data, f, ensure_ascii=False, indent=2)

                    # 更新列表
                    config_listbox.delete(selection[0])

                    # 更新下拉框
                    current_values = list(config_combobox['values'])
                    if config_name in current_values:
                        current_values.remove(config_name)
                        config_combobox['values'] = current_values

                    # 清空表单
                    name_var.set("")
                    desc_text.delete("1.0", "end")
                    standard_text.delete("1.0", "end")
                    categories_var.set("")
                    examples_text.delete("1.0", "end")

                    messagebox.showinfo("成功", f"配置 '{config_name}' 已删除")

                except Exception as e:
                    messagebox.showerror("错误", f"删除失败: {str(e)}")

        # 绑定列表选择事件
        config_listbox.bind("<<ListboxSelect>>", lambda e: load_config())

        # 按钮
        ttk.Button(btn_frame, text="保存配置", command=save_config).pack(side="left", padx=5)
        ttk.Button(btn_frame, text="删除配置", command=delete_config).pack(side="left", padx=5)
        ttk.Button(btn_frame, text="关闭", command=config_win.destroy).pack(side="left", padx=5)

        # 配置网格权重
        right_frame.columnconfigure(1, weight=1)

    def _close_ai_dialog(self, window):
        """关闭AI对话窗口并清除预览结果"""
        # 清除预览结果
        self.preview_result_df = None
        self.preview_code = None
        # 关闭窗口
        window.destroy()

    def _close_special_op(self, window):
        """关闭特殊操作窗口并清除预览结果"""
        # 如果是AI分类标注，还需要清除全局预览结果
        self.preview_result_df = None
        self.preview_code = None
        # 关闭窗口
        window.destroy()

    def _similarity_analysis(self, col1, col2, threshold, new_column_name, reorder=False):
        """分析两列数据的相似度
        
        如果启用重排功能：
        1. 不满足阈值的项会保持为None值，保证数据透明度
        2. 未匹配的第二列项会添加到DataFrame末尾作为额外行
        """
        df = self.current_df.copy() if self.current_df is not None else None
        if df is None:
            raise ValueError("当前没有加载数据")
        
        if col1 not in df.columns or col2 not in df.columns:
            raise ValueError(f"列名不存在: {col1 if col1 not in df.columns else col2}")
            
        try:
            threshold = float(threshold)
            if threshold < 0 or threshold > 1:
                raise ValueError("阈值必须在0到1之间")
        except ValueError:
            raise ValueError("阈值必须是0到1之间的数字")
            
        # 计算相似度
        similarities = []
        for i, row in df.iterrows():
            str1 = str(row[col1])
            str2 = str(row[col2])
            similarity = SequenceMatcher(None, str1, str2).ratio()
            similarities.append(similarity)
            
        # 添加相似度列
        df[new_column_name] = similarities
        
        # 添加是否超过阈值的标记列
        df[f"{new_column_name}_超过阈值"] = df[new_column_name] >= threshold
        
        # 创建一个新的DataFrame用于保存重排后的结果
        result_df = df.copy()
        
        # 统计信息（在重排之前先计算）
        above_threshold = sum(df[new_column_name] >= threshold)
        total_rows = len(df)
        percentage = above_threshold / total_rows * 100 if total_rows > 0 else 0
        
        # 根据参数决定是否重排内容
        if reorder:
            # 创建进度窗口
            progress_window = tk.Toplevel(self.root)
            progress_window.title("相似度排序处理")
            progress_window.geometry("400x150")
            progress_window.resizable(False, False)
            
            # 进度标签
            progress_label = ttk.Label(progress_window, text="正在进行相似度排序，请稍候...")
            progress_label.pack(pady=10)
            
            # 进度条
            progress_bar = ttk.Progressbar(progress_window, orient="horizontal", 
                                          length=350, mode="determinate")
            progress_bar.pack(pady=10)
            progress_bar["maximum"] = len(df)
            
            # 状态标签
            status_label = ttk.Label(progress_window, text="正在处理...")
            status_label.pack(pady=10)
            
            # 更新界面
            progress_window.update()
            
            try:
                # 获取列数据作为列表
                col1_values = df[col1].tolist()
                col2_values = df[col2].tolist().copy()  # 创建副本以便修改
                
                # 创建新的重排后列
                reordered_col2 = []
                matched_indices = set()  # 已匹配的第二列索引
                
                # 对于第一列的每一项，找到与之相似度最高的第二列项
                for i, val1 in enumerate(col1_values):
                    # 更新进度
                    progress_bar["value"] = i
                    status_label.configure(text=f"处理中: {i+1}/{len(df)}")
                    progress_window.update()
                    
                    best_match_idx = -1
                    best_match_similarity = -1
                    
                    # 遍历所有未匹配的第二列项，找到相似度最高的
                    for j, val2 in enumerate(col2_values):
                        if j not in matched_indices:
                            similarity = SequenceMatcher(None, str(val1), str(val2)).ratio()
                            if similarity > best_match_similarity:
                                best_match_similarity = similarity
                                best_match_idx = j
                    
                    # 如果找到的最佳匹配超过阈值，则使用它；否则使用空值
                    if best_match_idx != -1 and best_match_similarity >= threshold:
                        reordered_col2.append(col2_values[best_match_idx])
                        matched_indices.add(best_match_idx)
                    else:
                        reordered_col2.append(None)  # 没有合适的匹配
                
                # 不满足阈值的保持为None
                # 创建新列保存重排后的结果
                result_df[f"{col2}_重排"] = reordered_col2
                
                # 统计未匹配的第二列项
                unmatched_items = []
                for j, val2 in enumerate(col2_values):
                    if j not in matched_indices:
                        unmatched_items.append(val2)
                
                # 将未匹配的项添加到DataFrame末尾
                if unmatched_items:
                    # 创建额外的行
                    extra_rows = pd.DataFrame({f"{col2}_重排": unmatched_items})
                    # 其他列填充为None
                    for col in result_df.columns:
                        if col != f"{col2}_重排":
                            extra_rows[col] = None
                    # 添加到结果DataFrame
                    result_df = pd.concat([result_df, extra_rows], ignore_index=True)
                
                # 重新计算重排后的相似度
                new_similarities = []
                for i in range(len(result_df)):
                    val1 = result_df.iloc[i][col1]
                    val2 = result_df.iloc[i][f"{col2}_重排"]
                    if val1 is not None and val2 is not None:
                        similarity = SequenceMatcher(None, str(val1), str(val2)).ratio()
                    else:
                        similarity = 0
                    new_similarities.append(similarity)
                
                # 更新相似度列
                result_df[f"{new_column_name}_重排后"] = new_similarities
                result_df[f"{new_column_name}_重排后_超过阈值"] = result_df[f"{new_column_name}_重排后"] >= threshold
                
                # 关闭进度窗口
                progress_window.destroy()
                
                # 更新统计信息
                after_threshold = sum(result_df[f"{new_column_name}_重排后"] >= threshold)
                operation_desc = f"已计算列'{col1}'和'{col2}'的相似度，结果保存在'{new_column_name}'列\n"
                operation_desc += f"相似度超过{threshold}的有{above_threshold}行，占比{percentage:.2f}%\n"
                operation_desc += f"已重排'{col2}'列内容，重排后相似度超过阈值的有{after_threshold}行，占比{after_threshold/total_rows*100:.2f}%\n"
                operation_desc += f"重排内容保存在'{col2}_重排'列中"
            except Exception as e:
                progress_window.destroy()
                messagebox.showerror("重排失败", f"重排过程中出错: {str(e)}")
                # 如果重排失败，仍然返回原始结果
                operation_desc = f"已计算列'{col1}'和'{col2}'的相似度，结果保存在'{new_column_name}'列\n"
                operation_desc += f"相似度超过{threshold}的有{above_threshold}行，占比{percentage:.2f}%\n"
                operation_desc += f"重排失败: {str(e)}"
        else:
            operation_desc = f"已计算列'{col1}'和'{col2}'的相似度，结果保存在'{new_column_name}'列\n"
            operation_desc += f"相似度超过{threshold}的有{above_threshold}行，占比{percentage:.2f}%"
        
        return result_df, operation_desc

# 主程序
if __name__ == "__main__":
    root = tk.Tk()
    root.geometry("1100x700")
    app = ExcelSimpleApp(root)
    root.mainloop()