# -*- coding: utf-8 -*-
"""
测试streamlit集成功能
"""
import pandas as pd
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.getcwd())

def test_streamlit_integration():
    """测试streamlit集成功能"""
    print("开始测试streamlit集成...")
    
    try:
        # 导入主模块
        exec(open('local_ai_fixedV0.8.py', encoding='utf-8').read(), globals())
        # from local_ai_fixedV0_8 import ExcelSimpleApp
        print("✅ 成功导入ExcelSimpleApp")
        
        # 创建测试数据
        test_data = pd.DataFrame({
            '姓名': ['张三', '李四', '王五', '赵六'],
            '年龄': [25, 30, 35, 28],
            '城市': ['北京', '上海', '广州', '深圳'],
            '薪资': [8000, 12000, 15000, 10000]
        })
        print("✅ 创建测试数据成功")
        
        # 测试streamlit预览功能（不启动GUI）
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        app = ExcelSimpleApp(root)
        app.current_df = test_data
        print("✅ 创建应用实例成功")
        
        # 测试streamlit应用文件创建
        app_file, data_file = app._create_streamlit_app_file(test_data, "测试数据预览", "test")
        if app_file and data_file:
            print(f"✅ 成功创建streamlit应用文件: {app_file}")
            print(f"✅ 成功创建数据文件: {data_file}")
            
            # 检查文件是否存在
            if os.path.exists(app_file) and os.path.exists(data_file):
                print("✅ 文件创建验证成功")
                
                # 清理测试文件
                try:
                    os.remove(app_file)
                    os.remove(data_file)
                    print("✅ 测试文件清理成功")
                except:
                    print("⚠️ 测试文件清理失败，但不影响功能")
            else:
                print("❌ 文件创建验证失败")
        else:
            print("❌ streamlit应用文件创建失败")
        
        # 测试streamlit风格预览
        preview_text = app._create_streamlit_preview(test_data, "测试预览", max_rows=5)
        if preview_text and len(preview_text) > 0:
            print("✅ streamlit风格预览创建成功")
            print("预览内容示例:")
            print(preview_text[:200] + "..." if len(preview_text) > 200 else preview_text)
        else:
            print("❌ streamlit风格预览创建失败")
        
        root.destroy()
        print("✅ 所有测试完成")
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = test_streamlit_integration()
    if success:
        print("\n🎉 streamlit集成测试通过！")
        print("\n主要功能:")
        print("1. ✅ 数据预览 - 使用streamlit库实现数据显示")
        print("2. ✅ 结果预览 - 使用streamlit库实现结果显示")
        print("3. ✅ 自动生成streamlit应用文件")
        print("4. ✅ 浏览器中打开数据预览")
        print("5. ✅ 支持数据下载和交互")
    else:
        print("\n❌ streamlit集成测试失败")
    
    input("\n按回车键退出...")
