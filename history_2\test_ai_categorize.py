# -*- coding: utf-8 -*-
"""
AI智能分类功能测试脚本
"""
import pandas as pd
import json
import os

def create_test_data():
    """创建测试数据"""
    # 创建测试Excel文件
    test_data = {
        '产品名称': [
            'iPhone 15 Pro', 'Nike运动鞋', '可口可乐', '宜家沙发', 
            'MacBook Air', '优衣库T恤', '薯片', '台灯',
            'AirPods Pro', 'Adidas外套', '牛奶', '洗衣液',
            '小米手机', 'LV包包', '巧克力', '花瓶'
        ],
        '客户评论': [
            '很好用，推荐购买', '质量不错，满意', '太差了，不推荐', '一般般，还可以',
            '非常满意，会再买', '服务态度差', '还不错', '没什么特别的',
            '质量有问题', '很棒的产品', '普通', '不值这个价',
            '超级好用', '还行吧', '失望', '物超所值'
        ],
        '客户类型': [
            '年消费15万', '月购买1次', '6个月未购买', '注册2个月',
            '年消费8万', '1年未登录', '月购买3次', '首次购买',
            '年消费20万', '取消关注', '月购买2次', '注册1个月',
            '年消费3万', '会员等级金卡', '长时间未活跃', '新注册用户'
        ],
        '地址': [
            '北京市朝阳区', '成都市锦江区', '济南市历下区', '某某县城',
            '上海市浦东新区', '杭州市西湖区', '福州市鼓楼区', '某某镇',
            '广州市天河区', '重庆市渝中区', '合肥市蜀山区', '某某村',
            '深圳市南山区', '武汉市武昌区', '南昌市东湖区', '县级市'
        ]
    }
    
    df = pd.DataFrame(test_data)
    # 创建CSV文件（兼容性更好）
    df.to_csv('测试数据.csv', index=False, encoding='utf-8-sig')
    print("测试数据已创建: 测试数据.csv")

    # 如果有openpyxl，也创建Excel文件
    try:
        df.to_excel('测试数据.xlsx', index=False)
        print("测试数据已创建: 测试数据.xlsx")
    except ImportError:
        print("注意: 缺少openpyxl模块，无法创建Excel文件，但CSV文件可正常使用")

    return True

def test_config_loading():
    """测试配置文件加载"""
    config_file = "ai_categorize_config.json"
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print("✓ 配置文件加载成功")
            print(f"  可用配置: {list(config.get('分类配置', {}).keys())}")
            return True
        except Exception as e:
            print(f"✗ 配置文件加载失败: {e}")
            return False
    else:
        print("✗ 配置文件不存在")
        return False

def test_prompt_generation():
    """测试提示词生成"""
    try:
        # 模拟提示词生成
        category_prompt = "根据产品名称进行分类：电子产品、服装鞋帽、食品饮料、家居用品、其他"
        sample_data = ['iPhone 15 Pro', 'Nike运动鞋', '可口可乐']
        categories = ['电子产品', '服装鞋帽', '食品饮料', '家居用品', '其他']
        examples = {
            '电子产品': ['iPhone 15', 'MacBook Pro', 'AirPods'],
            '服装鞋帽': ['Nike运动鞋', '优衣库T恤', 'Adidas外套']
        }
        
        # 生成增强提示词
        base_prompt = f"""你是一个专业的数据分类专家。请根据以下分类标准对数据进行精确分类：

分类标准：
{category_prompt}

可选类别：{', '.join(categories)}

分类示例：
- 电子产品：iPhone 15, MacBook Pro, AirPods等
- 服装鞋帽：Nike运动鞋, 优衣库T恤, Adidas外套等

数据样本预览：{sample_data[:5]}

重要要求：
1. 严格按照给定的分类标准进行分类
2. 每个数据项必须分配到一个明确的类别
3. 输出格式：每行一个分类结果，不要有编号、标点符号或额外说明
4. 如果数据不清楚或无法分类，请输出"未知"
5. 保持与输入数据相同的顺序

"""
        print("✓ 提示词生成测试成功")
        print("生成的提示词长度:", len(base_prompt))
        return True
    except Exception as e:
        print(f"✗ 提示词生成测试失败: {e}")
        return False

def run_tests():
    """运行所有测试"""
    print("=== AI智能分类功能测试 ===\n")
    
    tests = [
        ("创建测试数据", create_test_data),
        ("配置文件加载", test_config_loading),
        ("提示词生成", test_prompt_generation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"测试: {test_name}")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
        print()
    
    print(f"=== 测试结果: {passed}/{total} 通过 ===")
    
    if passed == total:
        print("\n🎉 所有测试通过！AI智能分类功能已优化完成。")
        print("\n使用建议：")
        print("1. 启动Ollama服务")
        print("2. 运行 local_ai_fixedV0.7.py")
        print("3. 加载 '测试数据.xlsx' 进行测试")
        print("4. 尝试不同的预设配置")
        print("5. 查看 'AI分类功能使用说明.md' 了解详细用法")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查相关功能。")

if __name__ == "__main__":
    run_tests()
